#ifndef _BOARD_H_
#define _BOARD_H_
#include "stdio.h"
#include "string.h"
#include "ti_msp_dl_config.h"
#include "oled.h"
#include "led.h"
#include "key.h"
#include "motor.h"
#include "oled.h"
#include "encoder.h"
#include "show.h"
#include "control.h"
#include "DataScope_DP.h"
#include "uart_callback.h"
#include "adc.h"
#include "IR_Module.h"
#include "lcd.h"
#include "lcd_init.h"
#define ABS(a)      (a>0 ? a:(-a))
typedef int32_t  s32;
typedef int16_t s16;
typedef int8_t  s8;

typedef const int32_t sc32;  /*!< Read Only */
typedef const int16_t sc16;  /*!< Read Only */
typedef const int8_t sc8;   /*!< Read Only */

typedef __IO int32_t  vs32;
typedef __IO int16_t  vs16;
typedef __IO int8_t   vs8;

typedef __I int32_t vsc32;  /*!< Read Only */
typedef __I int16_t vsc16;  /*!< Read Only */
typedef __I int8_t vsc8;   /*!< Read Only */

typedef uint32_t  u32;
typedef uint16_t u16;
typedef uint8_t  u8;

typedef const uint32_t uc32;  /*!< Read Only */
typedef const uint16_t uc16;  /*!< Read Only */
typedef const uint8_t uc8;   /*!< Read Only */

typedef __IO uint32_t  vu32;
typedef __IO uint16_t vu16;
typedef __IO uint8_t  vu8;

typedef __I uint32_t vuc32;  /*!< Read Only */
typedef __I uint16_t vuc16;  /*!< Read Only */
typedef __I uint8_t vuc8;   /*!< Read Only */

// Enumeration of car types
//С���ͺŵ�ö�ٶ���
typedef enum 
{
	Mec_Car = 0, 
	Omni_Car, 
	Akm_Car, 
	Diff_Car, 
	FourWheel_Car, 
	Tank_Car
} CarMode;

extern u8 Way_Angle;                                                     //��ȡ�Ƕȵ��㷨��1����Ԫ��  2��������  3�������˲�
extern u16 determine;                              //�״����ģʽ��һ����־λ
extern int Motor_Left,Motor_Right;                                 //���PWM���� Ӧ��motor�� ��moto�¾�
extern u8 Flag_Stop,Flag_Show;                                           //ֹͣ��־λ�� ��ʾ��־λ Ĭ��ֹͣ ��ʾ��
extern int Middle_angle;                                                                             //��ص�ѹ������صı���
extern float Voltage,Angle_Balance,Gyro_Balance,Gyro_Turn;                           //ƽ����� ƽ�������� ת��������
extern u8 LD_Successful_Receive_flag;              //�״�ɹ��������ݱ�־λ
extern int Temperature;
extern u32 Distance;                                                //���������
extern u8 Flag_follow,Flag_avoid,delay_50,delay_flag,PID_Send;
extern float Acceleration_Z;                       //Z����ٶȼ�
extern float Balance_Kp,Balance_Kd,Velocity_Kp,Velocity_Ki,Turn_Kp,Turn_Kd;
extern float RC_Velocity,RC_Turn_Velocity,Move_X,Move_Y,Move_Z,PS2_ON_Flag;                //ң�ؿ��Ƶ��ٶ�
extern u8 one_frame_data_success_flag,one_lap_data_success_flag;
extern int lap_count,PointDataProcess_count,test_once_flag,Dividing_point;
extern u8 target_laps; // 目标圈数设置(1-10)
extern u8 current_laps; // 当前完成圈数
extern u8 corner_count; // 直角弯计数器
extern float Velocity_Left,Velocity_Right;  //�����ٶ�(mm/s)
extern uint8_t  recv0_flag;
extern u16 test_num,show_cnt;
extern u8 Car_Mode;
extern volatile unsigned long tick_ms;


//Systick������ֵ,24λ
#define SysTickMAX_COUNT 0xFFFFFF

//Systick����Ƶ��
#define SysTickFre 80000000

//��systick�ļ���ֵת��Ϊ�����ʱ�䵥λ
#define SysTick_MS(x)  ((SysTickFre/1000U)*(uint32_t)(x))
#define SysTick_US(x)  ((SysTickFre/1000000U)*(uint32_t)(x))

uint32_t Systick_getTick(void);
void delay_ms(uint32_t ms);
void delay_us(uint32_t us);
void delay_1us(unsigned long __us);
void delay_1ms(unsigned long ms);

// LCD GPIO定义 - 使用GPIOB端口
#define LCD_PORT        GPIOB
#define LCD_SCL_PIN     DL_GPIO_PIN_0   // SCL时钟线
#define LCD_SDA_PIN     DL_GPIO_PIN_1   // SDA数据线
#define LCD_RES_PIN     DL_GPIO_PIN_3   // 复位线
#define LCD_DC_PIN      DL_GPIO_PIN_4   // 数据/命令选择线
#define LCD_CS_PIN      DL_GPIO_PIN_5   // 片选线
#define LCD_BLK_PIN     DL_GPIO_PIN_8   // 背光控制线

// LCD显示函数声明
void LCD_Display_Status(void); // 显示当前状态

#endif  /* #ifndef _BOARD_H_ */