#include "key.h"

uint8_t keyValue(void)
{
    return ( DL_GPIO_readPins(KEY_key_PORT,KEY_key_PIN) & KEY_key_PIN ) > 0 ? 0:1;
}

UserKeyState_t key_scan(uint16_t freq)
{
    static uint16_t time_core;//��ʱ����
    static uint16_t long_press_time;//����ʶ��
    static uint8_t press_flag=0;//�������±��
    static uint8_t check_once=0;//�Ƿ��Ѿ�ʶ��1�α��
	
    float Count_time = (((float)(1.0f/(float)freq))*1000.0f);//�����1��Ҫ���ٸ�����

    if(check_once)//�����ʶ����������б���
    {
        press_flag=0;//�����1��ʶ�𣬱������
        time_core=0;//�����1��ʶ��ʱ������
        long_press_time=0;//�����1��ʶ��ʱ������
    }
    if(check_once&&1 == keyValue()) check_once=0; //���ɨ��󰴼�������������һ��ɨ��

    if(0==keyValue()&&check_once==0)//����ɨ��
    {
        press_flag=1;//��Ǳ�����1��
        long_press_time++;		  
    }

    if(long_press_time>(uint16_t)(500.0f/Count_time))// ����1��
    {	
        check_once=1;//����ѱ�ʶ��
        return USEKEY_long_click; //����
    }

    //����������1���ֵ���󣬿����ں���ʱ
    if(press_flag&&1==keyValue())
    {
        time_core++; 
    }		
	
    if(press_flag&&(time_core>(uint16_t)(50.0f/Count_time)&&time_core<(uint16_t)(300.0f/Count_time)))//50~700ms�ڱ��ٴΰ���
    {
        if(0==keyValue()) //����ٴΰ���
        {
            check_once=1;//����ѱ�ʶ��
            return USEKEY_double_click; //���Ϊ˫��
        }
    }
    else if(press_flag&&time_core>(uint16_t)(300.0f/Count_time))
    {
        check_once=1;//����ѱ�ʶ��
        return USEKEY_single_click; //800ms��û�����£����ǵ���
    }

    return USEKEY_stateless;
}

uint8_t lapsKeyValue(void)
{
    return ( DL_GPIO_readPins(KEY_laps_PORT,KEY_laps_PIN) & KEY_laps_PIN ) > 0 ? 0:1;
}

UserKeyState_t laps_key_scan(uint16_t freq)
{
    static uint16_t time_core;//计时核心
    static uint16_t long_press_time;//长按识别
    static uint8_t press_flag=0;//按键按下标记
    static uint8_t check_once=0;//是否已经识别1次按键

    float Count_time = (((float)(1.0f/(float)freq))*1000.0f);//计算1次要多少个周期

    if(check_once)//如果识别过，清除所有标记
    {
        press_flag=0;//清除1次识别，标志清零
        time_core=0;//清除1次识别时间清零
        long_press_time=0;//清除1次识别时间清零
    }
    if(check_once&&1 == lapsKeyValue()) check_once=0; //如果扫描到按键松开，清除下一次扫描

    if(0==lapsKeyValue()&&check_once==0)//按键扫描
    {
        press_flag=1;//标记按下过1次
        long_press_time++;
    }

    if(long_press_time>(uint16_t)(500.0f/Count_time))// 长按1秒
    {
        check_once=1;//标记已被识别
        return USEKEY_long_click; //长按
    }

    //按键松开过1次并且松开，开始计算后续时间
    if(press_flag&&1==lapsKeyValue())
    {
        time_core++;
    }

    if(press_flag&&(time_core>(uint16_t)(50.0f/Count_time)&&time_core<(uint16_t)(300.0f/Count_time)))//50~700ms内被再次按下
    {
        if(0==lapsKeyValue()) //如果再次按下
        {
            check_once=1;//标记已被识别
            return USEKEY_double_click; //认为是双击
        }
    }
    else if(press_flag&&time_core>(uint16_t)(300.0f/Count_time))
    {
        check_once=1;//标记已被识别
        return USEKEY_single_click; //800ms后没有按下，认为是单击
    }

    return USEKEY_stateless;
}


