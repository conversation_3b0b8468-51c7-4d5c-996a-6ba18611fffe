#ifndef IR_CONFIG_H
#define IR_CONFIG_H

// 转向角度参数配置 - 统一管理便于调试
#define TURN_90_ANGLE       3.8f    // 直角弯转向角度（rad/s）
#define MAX_TURN_ANGLE      1.8f    // 弯道最大转向角度（rad/s）- 避免摆动
#define MID_TURN_ANGLE      0.9f    // 弯道中等转向角度（rad/s）- 丢线补偿
#define MIN_TURN_ANGLE      1.8f    // 弯道最小转向角度（rad/s）- 微调温和
#define SHARP_TURN_ANGLE    2.5f    // 急弯专用角度（rad/s）- 大弯道使用

// 速度系数配置
#define SPEED_NORMAL        1.0f    // 正常巡线速度系数
#define SPEED_TURN_SHARP    0.8f    // 急弯时速度系数
#define SPEED_TURN_NORMAL   0.7f    // 普通转弯速度系数
#define SPEED_MICRO_ADJUST  0.8f    // 微调时速度系数
#define SPEED_LINE_LOST     -0.3f    // 丢线时速度系数

// 转弯状态机参数
#define TURN_STOP_TIME      10      // 转弯前停车时间(5ms周期)
#define TURN_ROTATE_TIME    55     // 原地转弯时间(5ms周期)
#define TURN_RECOVER_TIME   40      // 恢复巡线时间(5ms周期)
#define TURN_COOLDOWN_TIME  100     // 转弯冷却时间(5ms周期)

// 转弯角速度
#define TURN_ANGULAR_VEL    2.2f    // 原地转弯角速度（rad/s）

#endif // IR_CONFIG_H
