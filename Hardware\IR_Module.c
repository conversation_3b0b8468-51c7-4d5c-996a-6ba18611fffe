#include "IR_Module.h"
#include "IR_Config.h" // 引入配置文件
// 引脚状态变量
uint32_t ir_dh1_state, ir_dh2_state, ir_dh3_state, ir_dh4_state;
//转向参数调参时放大100倍 - 现在使用配置文件统一管理
float Turn90Angle  = TURN_90_ANGLE; // 直角弯转向角度（rad/s）
float maxTurnAngle = MAX_TURN_ANGLE; // 弯道最大转向角度（rad/s）
float midTurnAngle = MID_TURN_ANGLE; // 弯道中等转向角度（rad/s）
float minTurnAngle = MIN_TURN_ANGLE; // 弯道最小转向角度（rad/s）
float sharpTurnAngle = SHARP_TURN_ANGLE; // 急弯专用角度（rad/s）
//调参时速度参数放大1000倍

void IRDM_line_inspection(void) {
    static int last_state = 0; // 上一控制周期的状态
    static int ten_time = 0;
    static int turn_phase = 0; // 转弯阶段：0=正常巡线，1=停车，2=原地转弯，3=恢复巡线
    static int turn_counter = 0; // 转弯计数器
    static int turn_cooldown = 0; // 转弯冷却计数器，防止重复触发
    static int corner_detect_delay = 0; // 直角弯检测延时，防止重复计数
	float baseSpeed=RC_Velocity/1000.0f;      //巡线基础速度:速度过快弯道易出轨-可根据实际调整
	    // 读取四个引脚的状态并强制转换为0或1
    ir_dh4_state = DL_GPIO_readPins(IR_DH4_PORT, IR_DH4_PIN_17_PIN) ? 1 : 0;
    ir_dh3_state = DL_GPIO_readPins(IR_DH3_PORT, IR_DH3_PIN_16_PIN) ? 1 : 0;
    ir_dh2_state = DL_GPIO_readPins(IR_DH2_PORT, IR_DH2_PIN_12_PIN) ? 1 : 0;
    ir_dh1_state = DL_GPIO_readPins(IR_DH1_PORT, IR_DH1_PIN_27_PIN) ? 1 : 0;
	
    int DH_state = (ir_dh1_state << 3) | (ir_dh2_state << 2) | (ir_dh3_state << 1) | ir_dh4_state; // 将传感器状态组合成一个整数

    // 添加传感器状态调试信息
    printf("传感器: DH1=%d DH2=%d DH3=%d DH4=%d, 状态=%d\r\n",
           ir_dh1_state, ir_dh2_state, ir_dh3_state, ir_dh4_state, DH_state);
    // 更新转弯冷却计数器
    if (turn_cooldown > 0) {
        turn_cooldown--;
    }

    // 更新直角弯检测延时
    if (corner_detect_delay > 0) {
        corner_detect_delay--;
    }

    // 处理转弯状态机 - 转弯过程中不受传感器状态影响
    if (turn_phase > 0) {
        switch (turn_phase) {
            case 1: // 停车阶段
                Move_X = 0;
                Move_Z = 0;
                turn_counter++;
                if (turn_counter >= TURN_STOP_TIME) { // 停车时间配置化
                    turn_phase = 2;
                    turn_counter = 0;
                    //printf("开始原地转弯75°\r\n");
                }
                return; // 直接返回，不处理传感器状态

            case 2: // 原地转弯阶段 - 缩短转弯时间防止过头
                Move_X = 0; // 不前进，原地转弯
                Move_Z = (last_state == 2) ? TURN_ANGULAR_VEL : -TURN_ANGULAR_VEL; // 配置化转弯角速度
                turn_counter++;
                if (turn_counter >= TURN_ROTATE_TIME) { // 转弯时间配置化
                    turn_phase = 3;
                    turn_counter = 0;
                   // printf("转弯完成，开始恢复巡线\r\n");
                }
                return; // 直接返回，不处理传感器状态

            case 3: // 恢复巡线阶段
                Move_X = baseSpeed * 0.4f; // 稍微提高恢复速度
                Move_Z = 0;
                turn_counter++;
                if (turn_counter >= TURN_RECOVER_TIME) { // 恢复时间配置化
                    turn_phase = 0;
                    turn_counter = 0;
                    turn_cooldown = TURN_COOLDOWN_TIME; // 冷却时间配置化
                  //  printf("恢复正常巡线，启动2秒冷却\r\n");
                }
                return; // 直接返回，不处理传感器状态
        }
    }

    switch (DH_state) {
        case 0: // 0000 十字路口 - 停止两秒且鸣笛后直线通过
            ten_time++;
            if (ten_time < 1000) {
                Move_X = 0;
                Move_Z = 0;
            } else if (ten_time >= 1000) {
                Move_X = baseSpeed;
                Move_Z = 0;
            }
            last_state = 1;
            break;

        case 1: // 0001 左直角弯 - 逆时针正方形需要转弯
        case 3: // 0011 左直角弯（车头倾斜）
        case 5: // 0101 左直角弯（车头倾斜）
            ten_time = 0;
            // 只有在非转弯状态且冷却时间结束时才启动新的转弯
            if (turn_phase == 0 && turn_cooldown == 0) {
                turn_phase = 1; // 启动转弯状态机
                turn_counter = 0;
                last_state = 2; // 记录为左转状态
             //   printf("检测到左直角弯(状态%d)，启动转弯序列\r\n", DH_state);

                // 圈数计数逻辑
                if (corner_detect_delay == 0) {
                    corner_count++;
                    corner_detect_delay = CORNER_DETECT_DELAY; // 设置延时防止重复计数
                    LCD_Display_Status(); // 更新LCD显示

                    // 检查是否完成一圈
                    if (corner_count >= CORNERS_PER_LAP) {
                        current_laps++;
                        corner_count = 0; // 重置直角弯计数
                        LCD_Display_Status(); // 更新LCD显示

                        // 检查是否达到目标圈数 - 在最后一弯后检测到下一个左直角弯时停车
                        if (current_laps >= target_laps) {
                            Flag_Stop = 1; // 停车
                            current_laps = 0; // 重置圈数计数
                            corner_count = 0; // 重置直角弯计数
                            LCD_Display_Status(); // 更新LCD显示
                        }
                    }
                }
            } else if (turn_cooldown > 0) {
                // 在冷却期间，执行正常巡线而不是转弯
                Move_X = baseSpeed * 0.8f;
                Move_Z = minTurnAngle; // 轻微右转继续巡线
             //   printf("左直角弯冷却中，剩余%d个周期\r\n", turn_cooldown);
            }
            break;

        case 8: // 1000 右直角弯 - 逆时针寻迹时改为微调
        case 12: // 1100 右直角弯（车头倾斜）
        case 10: // 1010 右直角弯（车头倾斜）
            ten_time = 0;
            Move_X = baseSpeed * SPEED_TURN_SHARP; // 使用配置的急弯速度系数
            Move_Z = -maxTurnAngle;     // 使用标准最大转向角度
            last_state = 7;
            break;

        case 7: // 0111 左大弯
            ten_time = 0;
            Move_X = baseSpeed * SPEED_TURN_SHARP; // 使用配置的急弯速度系数
            Move_Z = sharpTurnAngle; // 使用急弯专用角度
            last_state = 4;
            break;

        case 14: // 1110 右大弯 - 逆时针寻迹时改为微调
            ten_time = 0;
            Move_X = baseSpeed * 0.7f; // 微调，不大幅转弯
            Move_Z = -sharpTurnAngle;     // 使用急弯专用角度
            last_state = 7;
            break;

        case 11: // 1011 左微调
            ten_time = 0;
            Move_X = baseSpeed * 0.7f;
            Move_Z = minTurnAngle;
            last_state = 6;
            break;

        case 13: // 1101 右微调
            ten_time = 0;
            Move_X = baseSpeed * 0.7f;
            Move_Z = -minTurnAngle;
            last_state = 7;
            break;

        case 9: // 1001 直行
            ten_time = 0;
            Move_X = baseSpeed;
            Move_Z = 0;
            last_state = 8;
            break;

        case 15://1111 丢线情况
            ten_time = 0;
			if(last_state==4||last_state==6){
				Move_X = baseSpeed * 0.9f; // 提高丢线时速度
				Move_Z = midTurnAngle; // 使用优化后的中等转向角度
			}
			if(last_state==5||last_state==7){
				Move_X = baseSpeed * 0.9f; // 提高丢线时速度
				Move_Z = -midTurnAngle; // 使用优化后的中等转向角度
			}
            break;

        // 添加其他可能的状态处理
        case 2: // 0010 轻微右偏
        case 4: // 0100 轻微右偏
        case 6: // 0110 中度右偏
            ten_time = 0;
            Move_X = baseSpeed * 0.9f;
            Move_Z = -minTurnAngle; // 轻微左转纠正
            last_state = 7;
            break;

        default: // 其他未处理状态，保持当前运动
            // 不改变Move_X和Move_Z，保持当前状态
            break;
    }
}








