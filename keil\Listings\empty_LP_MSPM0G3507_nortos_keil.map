Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to adc.o(.text.ADC1_IRQHandler) for ADC1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_callback.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to control.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) for SYSCFG_DL_ADC12_VOLTAGE_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for DL_ADC12_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for DL_ADC12_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for DL_GPIO_setUpperPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for DL_UART_enableFIFOs
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for DL_UART_setRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for DL_UART_setTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode) for DL_UART_enableLoopbackMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for DL_UART_enableDMAReceiveEvent
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for DL_ADC12_initSingleSample
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for DL_ADC12_configConversionMem
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for DL_ADC12_setSampleTime0
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus) for DL_ADC12_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt) for DL_ADC12_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for DL_ADC12_enableConversions
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.rodata.gADC12_VOLTAGEClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for SYSCFG_DL_DMA_CH0_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for DL_SYSTICK_enable
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableLoopbackMode) refers to ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    empty.o(.text.main) refers to empty.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    empty.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    empty.o(.text.main) refers to adc.o(.text.Get_battery_volt) for Get_battery_volt
    empty.o(.text.main) refers to uart_callback.o(.text.BTBufferHandler) for BTBufferHandler
    empty.o(.text.main) refers to show.o(.text.oled_show) for oled_show
    empty.o(.text.main) refers to show.o(.text.APP_Show) for APP_Show
    empty.o(.text.main) refers to empty.o(.bss.Voltage) for Voltage
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to empty.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    board.o(.text.fputc) refers to board.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    board.o(.text.fputc) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    board.o(.ARM.exidx.text.fputc) refers to board.o(.text.fputc) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.DL_UART_isBusy) refers to board.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    board.o(.text.fputs) refers to strlen.o(.text) for strlen
    board.o(.text.fputs) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    board.o(.ARM.exidx.text.fputs) refers to board.o(.text.fputs) for [Anonymous Symbol]
    board.o(.text.puts) refers to board.o(.text.fputs) for fputs
    board.o(.text.puts) refers to stdout.o(.data) for __stdout
    board.o(.text.puts) refers to board.o(.rodata.str1.1) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.puts) refers to board.o(.text.puts) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.Systick_getTick) refers to board.o(.text.Systick_getTick) for [Anonymous Symbol]
    board.o(.text.delay_ms) refers to board.o(.text.delay_us) for delay_us
    board.o(.ARM.exidx.text.delay_ms) refers to board.o(.text.delay_ms) for [Anonymous Symbol]
    board.o(.text.delay_us) refers to board.o(.text.Systick_getTick) for Systick_getTick
    board.o(.ARM.exidx.text.delay_us) refers to board.o(.text.delay_us) for [Anonymous Symbol]
    board.o(.text.delay_1us) refers to board.o(.text.delay_us) for delay_us
    board.o(.ARM.exidx.text.delay_1us) refers to board.o(.text.delay_1us) for [Anonymous Symbol]
    board.o(.text.delay_1ms) refers to board.o(.text.delay_ms) for delay_ms
    board.o(.ARM.exidx.text.delay_1ms) refers to board.o(.text.delay_1ms) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_disable) for DL_UART_disable
    dl_uart.o(.text.DL_UART_init) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_disable) refers to dl_uart.o(.text.DL_UART_disable) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_uart.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setOversampling) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setOversampling) refers to dl_uart.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setBaudRateDivisor) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to dl_uart.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for DL_UART_setIrDAPulseLength
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_isRXFIFOEmpty) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveData) refers to dl_uart.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_isTXFIFOFull) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitData) refers to dl_uart.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_enable) for DL_UART_enable
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_enable) refers to dl_uart.o(.text.DL_UART_enable) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_enable) for DL_UART_enable
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_trng.o(.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_sendCommand) for DL_TRNG_sendCommand
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_trng.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_trng.o(.text.DL_TRNG_sendCommand) refers to dl_trng.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_trng.o(.ARM.exidx.text.DL_TRNG_sendCommand) refers to dl_trng.o(.text.DL_TRNG_sendCommand) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for DL_Timer_setCounterValueAfterEnable
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setLoadValue) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCounterValueAfterEnable) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanConfig) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_timer.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_getInChanPairConfig) for DL_Timer_getInChanPairConfig
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanPairConfig) refers to dl_timer.o(.text.DL_Timer_getInChanPairConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for DL_Timer_setCaptureCompareAction
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for DL_Timer_setCounterValueAfterEnable
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for DL_Timer_setCaptureCompareAction
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_init) refers to dl_spi.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_spi.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_isRXFIFOEmpty) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_isTXFIFOFull) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_enable) for DL_SPI_enable
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_enable) refers to dl_spi.o(.text.DL_SPI_enable) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat) for DL_RTC_Common_setClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary) for DL_RTC_Common_setCalendarSecondsBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary) for DL_RTC_Common_setCalendarMinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary) for DL_RTC_Common_setCalendarHoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary) for DL_RTC_Common_setCalendarMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary) for DL_RTC_Common_setCalendarYearBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD) for DL_RTC_Common_setCalendarSecondsBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD) for DL_RTC_Common_setCalendarMinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD) for DL_RTC_Common_setCalendarHoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD) for DL_RTC_Common_setCalendarMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD) for DL_RTC_Common_setCalendarYearBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setClockFormat) refers to dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary) for DL_RTC_Common_getCalendarSecondsBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary) for DL_RTC_Common_getCalendarMinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary) for DL_RTC_Common_getCalendarHoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary) for DL_RTC_Common_getCalendarDayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary) for DL_RTC_Common_getCalendarDayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary) for DL_RTC_Common_getCalendarMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary) for DL_RTC_Common_getCalendarYearBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD) for DL_RTC_Common_getCalendarSecondsBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD) for DL_RTC_Common_getCalendarMinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD) for DL_RTC_Common_getCalendarHoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD) for DL_RTC_Common_getCalendarDayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD) for DL_RTC_Common_getCalendarDayOfMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD) for DL_RTC_Common_getCalendarMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD) for DL_RTC_Common_getCalendarYearBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getClockFormat) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary) for DL_RTC_Common_setAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary) for DL_RTC_Common_setAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) for DL_RTC_Common_setAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) for DL_RTC_Common_setAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD) for DL_RTC_Common_setAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD) for DL_RTC_Common_setAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) for DL_RTC_Common_setAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) for DL_RTC_Common_setAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary) for DL_RTC_Common_getAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary) for DL_RTC_Common_getAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) for DL_RTC_Common_getAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) for DL_RTC_Common_getAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD) for DL_RTC_Common_getAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD) for DL_RTC_Common_getAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) for DL_RTC_Common_getAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) for DL_RTC_Common_getAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary) for DL_RTC_Common_enableAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary) for DL_RTC_Common_enableAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) for DL_RTC_Common_enableAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) for DL_RTC_Common_enableAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD) for DL_RTC_Common_enableAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD) for DL_RTC_Common_enableAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) for DL_RTC_Common_enableAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) for DL_RTC_Common_enableAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary) for DL_RTC_Common_disableAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary) for DL_RTC_Common_disableAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) for DL_RTC_Common_disableAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) for DL_RTC_Common_disableAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD) for DL_RTC_Common_disableAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD) for DL_RTC_Common_disableAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) for DL_RTC_Common_disableAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) for DL_RTC_Common_disableAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary) for DL_RTC_Common_setAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary) for DL_RTC_Common_setAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) for DL_RTC_Common_setAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) for DL_RTC_Common_setAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD) for DL_RTC_Common_setAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD) for DL_RTC_Common_setAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) for DL_RTC_Common_setAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) for DL_RTC_Common_setAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary) for DL_RTC_Common_getAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary) for DL_RTC_Common_getAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) for DL_RTC_Common_getAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) for DL_RTC_Common_getAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD) for DL_RTC_Common_getAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD) for DL_RTC_Common_getAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) for DL_RTC_Common_getAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) for DL_RTC_Common_getAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary) for DL_RTC_Common_enableAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary) for DL_RTC_Common_enableAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) for DL_RTC_Common_enableAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) for DL_RTC_Common_enableAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD) for DL_RTC_Common_enableAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD) for DL_RTC_Common_enableAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) for DL_RTC_Common_enableAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) for DL_RTC_Common_enableAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary) for DL_RTC_Common_disableAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary) for DL_RTC_Common_disableAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) for DL_RTC_Common_disableAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) for DL_RTC_Common_disableAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD) for DL_RTC_Common_disableAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD) for DL_RTC_Common_disableAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) for DL_RTC_Common_disableAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) for DL_RTC_Common_disableAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_getGain) for DL_OPA_getGain
    dl_opa.o(.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_setGain) for DL_OPA_setGain
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_getGain) refers to dl_opa.o(.text.DL_OPA_getGain) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_setGain) refers to dl_opa.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_opa.o(.ARM.exidx.text.DL_OPA_setGain) refers to dl_opa.o(.text.DL_OPA_setGain) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_getGain) for DL_OPA_getGain
    dl_opa.o(.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_setGain) for DL_OPA_setGain
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_opa.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_mcan.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_RD_FIELD32_RAW) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_WR_FIELD32_RAW) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_RD_REG32_RAW) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_WR_REG32_RAW) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessUnlock) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessLock) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for DL_MCAN_getMsgObjSize
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsg) for DL_MCAN_writeMsg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getMsgObjSize) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getMsgObjSize) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for DL_MCAN_getDataSize
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.DL_MCAN_writeMsg) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for DL_MCAN_getMsgObjSize
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsg) for DL_MCAN_readMsg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for DL_MCAN_getDataSize
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.DL_MCAN_readMsg) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getDataSize) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getDataSize) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for [Anonymous Symbol]
    dl_mathacl.o(.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_setOperandTwo) for DL_MathACL_setOperandTwo
    dl_mathacl.o(.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_setOperandOne) for DL_MathACL_setOperandOne
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandTwo) refers to dl_mathacl.o(.text.DL_MathACL_setOperandTwo) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandOne) refers to dl_mathacl.o(.text.DL_MathACL_setOperandOne) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_i2c.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull) for DL_I2C_isControllerTXFIFOFull
    dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_transmitControllerData) for DL_I2C_transmitControllerData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOFull) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitControllerData) refers to dl_i2c.o(.text.DL_I2C_transmitControllerData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO) for DL_I2C_startFlushControllerTXFIFO
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty) for DL_I2C_isControllerTXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO) for DL_I2C_stopFlushControllerTXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO) for DL_I2C_startFlushControllerRXFIFO
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty) for DL_I2C_isControllerRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO) for DL_I2C_stopFlushControllerRXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerRXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for DL_I2C_isTargetTXFIFOFull
    dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOFull) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetData) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO) for DL_I2C_startFlushTargetTXFIFO
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty) for DL_I2C_isTargetTXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO) for DL_I2C_stopFlushTargetTXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO) for DL_I2C_startFlushTargetRXFIFO
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for DL_I2C_isTargetRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO) for DL_I2C_stopFlushTargetRXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetRXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for DL_I2C_getTargetStatus
    dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getTargetStatus) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for DL_I2C_isTargetTXFIFOFull
    dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for DL_I2C_getTargetStatus
    dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for DL_I2C_receiveTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetData) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for DL_I2C_isTargetRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for DL_I2C_receiveTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setCommandAddress) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_waitForCmdDone) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for DL_FlashCTL_unprotectDataMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for DL_FlashCTL_eraseDataBankFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for DL_FlashCTL_unprotectDataMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled) for DL_SYSCTL_isFlashBankSwapEnabled
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode) for DL_FlashCTL_enableAddressOverrideMode
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) for DL_FlashCTL_setBankSelect
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) for DL_FlashCTL_setRegionSelect
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode) for DL_FlashCTL_disableAddressOverrideMode
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isFlashBankSwapEnabled) refers to dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_enableAddressOverrideMode) refers to dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) refers to dl_flashctl.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setBankSelect) refers to dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) refers to dl_flashctl.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setRegionSelect) refers to dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_disableAddressOverrideMode) refers to dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for DL_FlashCTL_massErase
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for DL_FlashCTL_massEraseFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for DL_FlashCTL_programMemory64WithECCGenerated
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for DL_FlashCTL_getFlashSectorNumberInBank
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for DL_SYSCTL_isExecuteFromUpperFlashBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for DL_FlashCTL_programMemoryFromRAM64WithECCGenerated
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for DL_FlashCTL_programMemory64WithECCManual
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for DL_FlashCTL_programMemoryFromRAM64WithECCManual
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for DL_FlashCTL_programMemory32
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for DL_FlashCTL_programMemory64
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for DL_FlashCTL_programMemoryFromRAM32
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for DL_FlashCTL_programMemoryFromRAM64
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumber) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isExecuteFromUpperFlashBank) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for DL_FlashCTL_getFlashSectorNumberInBank
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for DL_SYSCTL_isExecuteFromUpperFlashBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_CORE_getInstructionConfig) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_CORE_configInstruction) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_flashctl.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for [Anonymous Symbol]
    dl_dma.o(.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_configTransfer) for DL_DMA_configTransfer
    dl_dma.o(.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_setTrigger) for DL_DMA_setTrigger
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_configTransfer) refers to dl_dma.o(.text.DL_DMA_configTransfer) for [Anonymous Symbol]
    dl_dma.o(.text.DL_DMA_setTrigger) refers to dl_dma.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dma.o(.ARM.exidx.text.DL_DMA_setTrigger) refers to dl_dma.o(.text.DL_DMA_setTrigger) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_dma.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_configDataFormat) for DL_DAC12_configDataFormat
    dl_dac12.o(.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_configDataFormat) refers to dl_dac12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_configDataFormat) refers to dl_dac12.o(.text.DL_DAC12_configDataFormat) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_dac12.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_output8) for DL_DAC12_output8
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_isFIFOFull) refers to dl_dac12.o(.text.DL_DAC12_getInterruptStatus) for DL_DAC12_getInterruptStatus
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_isFIFOFull) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_output8) refers to dl_dac12.o(.text.DL_DAC12_output8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_output12) for DL_DAC12_output12
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_output12) refers to dl_dac12.o(.text.DL_DAC12_output12) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_output8) for DL_DAC12_output8
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_output12) for DL_DAC12_output12
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_startCalibration) for DL_DAC12_startCalibration
    dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_isCalibrationRunning) for DL_DAC12_isCalibrationRunning
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_startCalibration) refers to dl_dac12.o(.text.DL_DAC12_startCalibration) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_isCalibrationRunning) refers to dl_dac12.o(.text.DL_DAC12_isCalibrationRunning) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_getInterruptStatus) refers to dl_dac12.o(.text.DL_DAC12_getInterruptStatus) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for DL_CRC_setSeed32
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_feedData32) for DL_CRC_feedData32
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_getResult32) for DL_CRC_getResult32
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_feedData32) refers to dl_crc.o(.text.DL_CRC_feedData32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_getResult32) refers to dl_crc.o(.text.DL_CRC_getResult32) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for DL_CRC_setSeed32
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_feedData32) for DL_CRC_feedData32
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_getResult32) for DL_CRC_getResult32
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for DL_CRC_setSeed16
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_feedData16) for DL_CRC_feedData16
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_getResult16) for DL_CRC_getResult16
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_feedData16) refers to dl_crc.o(.text.DL_CRC_feedData16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_getResult16) refers to dl_crc.o(.text.DL_CRC_getResult16) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for DL_CRC_setSeed16
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_feedData16) for DL_CRC_feedData16
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_getResult16) for DL_CRC_getResult16
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for DL_AES_setKeyAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_checkAlignmentAndReturnPointer) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataWord) refers to dl_aes.o(.text.DL_AES_loadDataWord) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for DL_AES_loadDataInAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for DL_AES_getDataOutAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for DL_AES_loadXORDataInAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for DL_AES_loadXORDataInWithoutTriggerAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for DL_AES_xorDataAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_adc12.o(.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_adc12.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to startup_mspm0g350x_uvision.o(.text) for Default_Handler
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) for [Anonymous Symbol]
    oled.o(.text.OLED_Refresh_Gram) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Refresh_Gram) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Refresh_Gram) refers to oled.o(.text.OLED_Refresh_Gram) for [Anonymous Symbol]
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_RS_Set) for OLED_RS_Set
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_RS_Clr) for OLED_RS_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SCLK_Clr) for OLED_SCLK_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SDIN_Set) for OLED_SDIN_Set
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SDIN_Clr) for OLED_SDIN_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SCLK_Set) for OLED_SCLK_Set
    oled.o(.ARM.exidx.text.OLED_WR_Byte) refers to oled.o(.text.OLED_WR_Byte) for [Anonymous Symbol]
    oled.o(.text.OLED_RS_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_RS_Set) refers to oled.o(.text.OLED_RS_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_RS_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_RS_Clr) refers to oled.o(.text.OLED_RS_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SCLK_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_SCLK_Clr) refers to oled.o(.text.OLED_SCLK_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SDIN_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_SDIN_Set) refers to oled.o(.text.OLED_SDIN_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_SDIN_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_SDIN_Clr) refers to oled.o(.text.OLED_SDIN_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SCLK_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_SCLK_Set) refers to oled.o(.text.OLED_SCLK_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_On) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Display_On) refers to oled.o(.text.OLED_Display_On) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_Off) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Display_Off) refers to oled.o(.text.OLED_Display_Off) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.text.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(.text.OLED_Clear) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawPoint) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_DrawPoint) refers to oled.o(.text.OLED_DrawPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.oled_asc2_1608) for oled_asc2_1608
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.oled_asc2_1206) for oled_asc2_1206
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.oled_pow) refers to oled.o(.text.oled_pow) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNumber) refers to oled.o(.text.oled_pow) for oled_pow
    oled.o(.text.OLED_ShowNumber) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    oled.o(.text.OLED_ShowNumber) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowNumber) refers to oled.o(.text.OLED_ShowNumber) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_RST_Clr) for OLED_RST_Clr
    oled.o(.text.OLED_Init) refers to board.o(.text.delay_ms) for delay_ms
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_RST_Set) for OLED_RST_Set
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_RST_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_RST_Clr) refers to oled.o(.text.OLED_RST_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_RST_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_RST_Set) refers to oled.o(.text.OLED_RST_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.rodata.Hzk16) for Hzk16
    oled.o(.ARM.exidx.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_ShowCHinese) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Pos) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to oled.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_setPins) refers to oled.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.text.keyValue) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.ARM.exidx.text.keyValue) refers to key.o(.text.keyValue) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to ffltui.o(.text) for __aeabi_ui2f
    key.o(.text.key_scan) refers to fdiv.o(.text) for __aeabi_fdiv
    key.o(.text.key_scan) refers to fmul.o(.text) for __aeabi_fmul
    key.o(.text.key_scan) refers to key.o(.text.keyValue) for keyValue
    key.o(.text.key_scan) refers to ffixi.o(.text) for __aeabi_f2iz
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.check_once) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.press_flag) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.time_core) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.long_press_time) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.key_scan) refers to key.o(.text.key_scan) for [Anonymous Symbol]
    key.o(.text.lapsKeyValue) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.ARM.exidx.text.lapsKeyValue) refers to key.o(.text.lapsKeyValue) for [Anonymous Symbol]
    key.o(.text.laps_key_scan) refers to ffltui.o(.text) for __aeabi_ui2f
    key.o(.text.laps_key_scan) refers to fdiv.o(.text) for __aeabi_fdiv
    key.o(.text.laps_key_scan) refers to fmul.o(.text) for __aeabi_fmul
    key.o(.text.laps_key_scan) refers to key.o(.text.lapsKeyValue) for lapsKeyValue
    key.o(.text.laps_key_scan) refers to ffixi.o(.text) for __aeabi_f2iz
    key.o(.text.laps_key_scan) refers to key.o(.bss.laps_key_scan.check_once) for [Anonymous Symbol]
    key.o(.text.laps_key_scan) refers to key.o(.bss.laps_key_scan.press_flag) for [Anonymous Symbol]
    key.o(.text.laps_key_scan) refers to key.o(.bss.laps_key_scan.time_core) for [Anonymous Symbol]
    key.o(.text.laps_key_scan) refers to key.o(.bss.laps_key_scan.long_press_time) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.laps_key_scan) refers to key.o(.text.laps_key_scan) for [Anonymous Symbol]
    led.o(.text.LED_ON) refers to led.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    led.o(.ARM.exidx.text.LED_ON) refers to led.o(.text.LED_ON) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to led.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    led.o(.text.LED_OFF) refers to led.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    led.o(.ARM.exidx.text.LED_OFF) refers to led.o(.text.LED_OFF) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_setPins) refers to led.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    led.o(.text.LED_Toggle) refers to led.o(.text.DL_GPIO_togglePins) for DL_GPIO_togglePins
    led.o(.ARM.exidx.text.LED_Toggle) refers to led.o(.text.LED_Toggle) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_togglePins) refers to led.o(.text.DL_GPIO_togglePins) for [Anonymous Symbol]
    led.o(.text.LED_Flash) refers to led.o(.text.LED_ON) for LED_ON
    led.o(.text.LED_Flash) refers to led.o(.text.LED_Toggle) for LED_Toggle
    led.o(.text.LED_Flash) refers to led.o(.bss.LED_Flash.temp) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.LED_Flash) refers to led.o(.text.LED_Flash) for [Anonymous Symbol]
    motor.o(.text.Set_PWM) refers to motor.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor.o(.text.Set_PWM) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.text.Set_PWM) refers to motor.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    motor.o(.ARM.exidx.text.Set_PWM) refers to motor.o(.text.Set_PWM) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to motor.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.DL_GPIO_setPins) refers to motor.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.gpio_interrup1) for gpio_interrup1
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.gpio_interrup2) for gpio_interrup2
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countA) for Get_Encoder_countA
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countB) for Get_Encoder_countB
    encoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_readPins) refers to encoder.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to encoder.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    adc.o(.text.Get_battery_volt) refers to adc.o(.text.DL_ADC12_startConversion) for DL_ADC12_startConversion
    adc.o(.text.Get_battery_volt) refers to adc.o(.text.DL_ADC12_getMemResult) for DL_ADC12_getMemResult
    adc.o(.text.Get_battery_volt) refers to dflti.o(.text) for __aeabi_i2d
    adc.o(.text.Get_battery_volt) refers to dmul.o(.text) for __aeabi_dmul
    adc.o(.text.Get_battery_volt) refers to ddiv.o(.text) for __aeabi_ddiv
    adc.o(.text.Get_battery_volt) refers to d2f.o(.text) for __aeabi_d2f
    adc.o(.text.Get_battery_volt) refers to adc.o(.bss.gCheckADC) for gCheckADC
    adc.o(.ARM.exidx.text.Get_battery_volt) refers to adc.o(.text.Get_battery_volt) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.DL_ADC12_startConversion) refers to adc.o(.text.DL_ADC12_startConversion) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.DL_ADC12_getMemResult) refers to adc.o(.text.DL_ADC12_getMemResult) for [Anonymous Symbol]
    adc.o(.text.ADC1_IRQHandler) refers to adc.o(.text.DL_ADC12_getPendingInterrupt) for DL_ADC12_getPendingInterrupt
    adc.o(.text.ADC1_IRQHandler) refers to adc.o(.bss.gCheckADC) for gCheckADC
    adc.o(.ARM.exidx.text.ADC1_IRQHandler) refers to adc.o(.text.ADC1_IRQHandler) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.DL_ADC12_getPendingInterrupt) refers to adc.o(.text.DL_ADC12_getPendingInterrupt) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to fdiv.o(.text) for __aeabi_fdiv
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    ir_module.o(.text.IRDM_line_inspection) refers to printfa.o(i.__0printf) for printf
    ir_module.o(.text.IRDM_line_inspection) refers to fmul.o(.text) for __aeabi_fmul
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.data.RC_Velocity) for RC_Velocity
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.ir_dh4_state) for ir_dh4_state
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.ir_dh3_state) for ir_dh3_state
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.ir_dh2_state) for ir_dh2_state
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.ir_dh1_state) for ir_dh1_state
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.rodata.str1.1) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.turn_cooldown) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.corner_detect_delay) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.turn_phase) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.bss.Move_X) for Move_X
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.bss.Move_Z) for Move_Z
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.turn_counter) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.last_state) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.ten_time) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.data.minTurnAngle) for minTurnAngle
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.data.midTurnAngle) for midTurnAngle
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.data.sharpTurnAngle) for sharpTurnAngle
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.data.maxTurnAngle) for maxTurnAngle
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.bss.corner_count) for corner_count
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.bss.current_laps) for current_laps
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.data.target_laps) for target_laps
    ir_module.o(.text.IRDM_line_inspection) refers to control.o(.data.Flag_Stop) for Flag_Stop
    ir_module.o(.ARM.exidx.text.IRDM_line_inspection) refers to ir_module.o(.text.IRDM_line_inspection) for [Anonymous Symbol]
    ir_module.o(.ARM.exidx.text.DL_GPIO_readPins) refers to ir_module.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Key) for Key
    control.o(.text.TIMG0_IRQHandler) refers to led.o(.text.LED_Flash) for LED_Flash
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Get_Velocity_From_Encoder) for Get_Velocity_From_Encoder
    control.o(.text.TIMG0_IRQHandler) refers to ir_module.o(.text.IRDM_line_inspection) for IRDM_line_inspection
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Get_Target_Encoder) for Get_Target_Encoder
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Incremental_PI_Left) for Incremental_PI_Left
    control.o(.text.TIMG0_IRQHandler) refers to fflti.o(.text) for __aeabi_i2f
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Incremental_PI_Right) for Incremental_PI_Right
    control.o(.text.TIMG0_IRQHandler) refers to f2d.o(.text) for __aeabi_f2d
    control.o(.text.TIMG0_IRQHandler) refers to printfa.o(i.__0printf) for printf
    control.o(.text.TIMG0_IRQHandler) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text.TIMG0_IRQHandler) refers to motor.o(.text.Set_PWM) for Set_PWM
    control.o(.text.TIMG0_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countA) for Get_Encoder_countA
    control.o(.text.TIMG0_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countB) for Get_Encoder_countB
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.data.Flag_Stop) for Flag_Stop
    control.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.Move_X) for Move_X
    control.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.Move_Z) for Move_Z
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.bss.MotorA) for MotorA
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.bss.MotorB) for MotorB
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.rodata.str1.1) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to control.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to control.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    control.o(.text.Key) refers to key.o(.text.key_scan) for key_scan
    control.o(.text.Key) refers to key.o(.text.laps_key_scan) for laps_key_scan
    control.o(.text.Key) refers to control.o(.data.Flag_Stop) for Flag_Stop
    control.o(.text.Key) refers to empty.o(.bss.current_laps) for current_laps
    control.o(.text.Key) refers to empty.o(.bss.corner_count) for corner_count
    control.o(.text.Key) refers to empty.o(.data.target_laps) for target_laps
    control.o(.ARM.exidx.text.Key) refers to control.o(.text.Key) for [Anonymous Symbol]
    control.o(.text.Get_Velocity_From_Encoder) refers to fflti.o(.text) for __aeabi_i2f
    control.o(.text.Get_Velocity_From_Encoder) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text.Get_Velocity_From_Encoder) refers to f2d.o(.text) for __aeabi_f2d
    control.o(.text.Get_Velocity_From_Encoder) refers to dmul.o(.text) for __aeabi_dmul
    control.o(.text.Get_Velocity_From_Encoder) refers to ddiv.o(.text) for __aeabi_ddiv
    control.o(.text.Get_Velocity_From_Encoder) refers to d2f.o(.text) for __aeabi_d2f
    control.o(.text.Get_Velocity_From_Encoder) refers to control.o(.bss.OriginalEncoder) for OriginalEncoder
    control.o(.text.Get_Velocity_From_Encoder) refers to control.o(.bss.MotorA) for MotorA
    control.o(.text.Get_Velocity_From_Encoder) refers to control.o(.bss.MotorB) for MotorB
    control.o(.ARM.exidx.text.Get_Velocity_From_Encoder) refers to control.o(.text.Get_Velocity_From_Encoder) for [Anonymous Symbol]
    control.o(.text.Get_Target_Encoder) refers to fcmpge.o(.text) for __aeabi_fcmpge
    control.o(.text.Get_Target_Encoder) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text.Get_Target_Encoder) refers to fdiv.o(.text) for __aeabi_fdiv
    control.o(.text.Get_Target_Encoder) refers to fadd.o(.text) for __aeabi_fsub
    control.o(.text.Get_Target_Encoder) refers to control.o(.bss.MotorA) for MotorA
    control.o(.text.Get_Target_Encoder) refers to control.o(.bss.MotorB) for MotorB
    control.o(.ARM.exidx.text.Get_Target_Encoder) refers to control.o(.text.Get_Target_Encoder) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Left) refers to fadd.o(.text) for __aeabi_fsub
    control.o(.text.Incremental_PI_Left) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text.Incremental_PI_Left) refers to fcmple.o(.text) for __aeabi_fcmple
    control.o(.text.Incremental_PI_Left) refers to fcmpge.o(.text) for __aeabi_fcmpge
    control.o(.text.Incremental_PI_Left) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text.Incremental_PI_Left) refers to control.o(.bss.Incremental_PI_Left.Bias) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Left) refers to control.o(.data.Velocity_KP) for Velocity_KP
    control.o(.text.Incremental_PI_Left) refers to control.o(.bss.Incremental_PI_Left.Last_bias) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Left) refers to control.o(.data.Velocity_KI) for Velocity_KI
    control.o(.text.Incremental_PI_Left) refers to control.o(.bss.Incremental_PI_Left.Pwm) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.Incremental_PI_Left) refers to control.o(.text.Incremental_PI_Left) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Right) refers to fadd.o(.text) for __aeabi_fsub
    control.o(.text.Incremental_PI_Right) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text.Incremental_PI_Right) refers to fcmple.o(.text) for __aeabi_fcmple
    control.o(.text.Incremental_PI_Right) refers to fcmpge.o(.text) for __aeabi_fcmpge
    control.o(.text.Incremental_PI_Right) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text.Incremental_PI_Right) refers to control.o(.bss.Incremental_PI_Right.Bias) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Right) refers to control.o(.data.Velocity_KP) for Velocity_KP
    control.o(.text.Incremental_PI_Right) refers to control.o(.bss.Incremental_PI_Right.Last_bias) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Right) refers to control.o(.data.Velocity_KI) for Velocity_KI
    control.o(.text.Incremental_PI_Right) refers to control.o(.bss.Incremental_PI_Right.Pwm) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.Incremental_PI_Right) refers to control.o(.text.Incremental_PI_Right) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.myabs) refers to control.o(.text.myabs) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.Turn_Off) refers to control.o(.text.Turn_Off) for [Anonymous Symbol]
    control.o(.text.PWM_Limit) refers to fcmple.o(.text) for __aeabi_fcmple
    control.o(.text.PWM_Limit) refers to fcmpge.o(.text) for __aeabi_fcmpge
    control.o(.ARM.exidx.text.PWM_Limit) refers to control.o(.text.PWM_Limit) for [Anonymous Symbol]
    show.o(.text.oled_show) refers to memseta.o(.text) for __aeabi_memclr
    show.o(.text.oled_show) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    show.o(.text.oled_show) refers to oled.o(.text.OLED_ShowNumber) for OLED_ShowNumber
    show.o(.text.oled_show) refers to fcmpge.o(.text) for __aeabi_fcmpge
    show.o(.text.oled_show) refers to fcmplt.o(.text) for __aeabi_fcmplt
    show.o(.text.oled_show) refers to fmul.o(.text) for __aeabi_fmul
    show.o(.text.oled_show) refers to ffixi.o(.text) for __aeabi_f2iz
    show.o(.text.oled_show) refers to control.o(.text.myabs) for myabs
    show.o(.text.oled_show) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    show.o(.text.oled_show) refers to empty.o(.data.Car_Mode) for Car_Mode
    show.o(.text.oled_show) refers to show.o(.rodata.str1.1) for [Anonymous Symbol]
    show.o(.text.oled_show) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    show.o(.text.oled_show) refers to oled.o(.text.OLED_Refresh_Gram) for OLED_Refresh_Gram
    show.o(.text.oled_show) refers to control.o(.data.Run_Mode) for Run_Mode
    show.o(.text.oled_show) refers to ir_module.o(.bss.ir_dh1_state) for ir_dh1_state
    show.o(.text.oled_show) refers to ir_module.o(.bss.ir_dh2_state) for ir_dh2_state
    show.o(.text.oled_show) refers to ir_module.o(.bss.ir_dh3_state) for ir_dh3_state
    show.o(.text.oled_show) refers to ir_module.o(.bss.ir_dh4_state) for ir_dh4_state
    show.o(.text.oled_show) refers to empty.o(.data.target_laps) for target_laps
    show.o(.text.oled_show) refers to empty.o(.bss.current_laps) for current_laps
    show.o(.text.oled_show) refers to empty.o(.bss.corner_count) for corner_count
    show.o(.text.oled_show) refers to empty.o(.bss.Move_Z) for Move_Z
    show.o(.text.oled_show) refers to control.o(.bss.MotorA) for MotorA
    show.o(.text.oled_show) refers to control.o(.bss.MotorB) for MotorB
    show.o(.text.oled_show) refers to empty.o(.bss.Voltage) for Voltage
    show.o(.text.oled_show) refers to control.o(.data.Flag_Stop) for Flag_Stop
    show.o(.ARM.exidx.text.oled_show) refers to show.o(.text.oled_show) for [Anonymous Symbol]
    show.o(.text.APP_Show) refers to fadd.o(.text) for __aeabi_fadd
    show.o(.text.APP_Show) refers to fdiv.o(.text) for __aeabi_fdiv
    show.o(.text.APP_Show) refers to ffixi.o(.text) for __aeabi_f2iz
    show.o(.text.APP_Show) refers to f2d.o(.text) for __aeabi_f2d
    show.o(.text.APP_Show) refers to dmul.o(.text) for __aeabi_dmul
    show.o(.text.APP_Show) refers to dfixi.o(.text) for __aeabi_d2iz
    show.o(.text.APP_Show) refers to fmul.o(.text) for __aeabi_fmul
    show.o(.text.APP_Show) refers to printfa.o(i.__0printf) for printf
    show.o(.text.APP_Show) refers to empty.o(.bss.Voltage) for Voltage
    show.o(.text.APP_Show) refers to empty.o(.bss.Velocity_Right) for Velocity_Right
    show.o(.text.APP_Show) refers to empty.o(.bss.Velocity_Left) for Velocity_Left
    show.o(.text.APP_Show) refers to show.o(.bss.APP_Show.flag) for [Anonymous Symbol]
    show.o(.text.APP_Show) refers to empty.o(.bss.PID_Send) for PID_Send
    show.o(.text.APP_Show) refers to show.o(.rodata.str1.1) for [Anonymous Symbol]
    show.o(.text.APP_Show) refers to empty.o(.data.RC_Velocity) for RC_Velocity
    show.o(.text.APP_Show) refers to control.o(.data.Velocity_KP) for Velocity_KP
    show.o(.text.APP_Show) refers to control.o(.data.Velocity_KI) for Velocity_KI
    show.o(.text.APP_Show) refers to ir_module.o(.data.Turn90Angle) for Turn90Angle
    show.o(.text.APP_Show) refers to ir_module.o(.data.maxTurnAngle) for maxTurnAngle
    show.o(.text.APP_Show) refers to ir_module.o(.data.midTurnAngle) for midTurnAngle
    show.o(.text.APP_Show) refers to ir_module.o(.data.minTurnAngle) for minTurnAngle
    show.o(.ARM.exidx.text.APP_Show) refers to show.o(.text.APP_Show) for [Anonymous Symbol]
    show.o(.text.DataScope) refers to datascope_dp.o(.text.DataScope_Get_Channel_Data) for DataScope_Get_Channel_Data
    show.o(.text.DataScope) refers to datascope_dp.o(.text.DataScope_Data_Generate) for DataScope_Data_Generate
    show.o(.ARM.exidx.text.DataScope) refers to show.o(.text.DataScope) for [Anonymous Symbol]
    datascope_dp.o(.ARM.exidx.text.Float2Byte) refers to datascope_dp.o(.text.Float2Byte) for [Anonymous Symbol]
    datascope_dp.o(.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.text.Float2Byte) for Float2Byte
    datascope_dp.o(.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.bss.DataScope_OutPut_Buffer) for DataScope_OutPut_Buffer
    datascope_dp.o(.ARM.exidx.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.text.DataScope_Get_Channel_Data) for [Anonymous Symbol]
    datascope_dp.o(.text.DataScope_Data_Generate) refers to datascope_dp.o(.bss.DataScope_OutPut_Buffer) for DataScope_OutPut_Buffer
    datascope_dp.o(.ARM.exidx.text.DataScope_Data_Generate) refers to datascope_dp.o(.text.DataScope_Data_Generate) for [Anonymous Symbol]
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_disableChannel) for DL_DMA_disableChannel
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setSrcAddr) for DL_DMA_setSrcAddr
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setDestAddr) for DL_DMA_setDestAddr
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setTransferSize) for DL_DMA_setTransferSize
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_enableChannel) for DL_DMA_enableChannel
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.bss.gBTPacket) for gBTPacket
    uart_callback.o(.ARM.exidx.text.BT_DAMConfig) refers to uart_callback.o(.text.BT_DAMConfig) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_disableChannel) refers to uart_callback.o(.text.DL_DMA_disableChannel) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setSrcAddr) refers to uart_callback.o(.text.DL_DMA_setSrcAddr) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setDestAddr) refers to uart_callback.o(.text.DL_DMA_setDestAddr) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setTransferSize) refers to uart_callback.o(.text.DL_DMA_setTransferSize) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_enableChannel) refers to uart_callback.o(.text.DL_DMA_enableChannel) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.DL_DMA_getTransferSize) for DL_DMA_getTransferSize
    uart_callback.o(.text.BTBufferHandler) refers to board.o(.text.Systick_getTick) for Systick_getTick
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.bt_control) for bt_control
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.BT_DAMConfig) for BT_DAMConfig
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.lastSize) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.handleflag) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.handleSize) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.gBTPacket) for gBTPacket
    uart_callback.o(.ARM.exidx.text.BTBufferHandler) refers to uart_callback.o(.text.BTBufferHandler) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_getTransferSize) refers to uart_callback.o(.text.DL_DMA_getTransferSize) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to fadd.o(.text) for __aeabi_fadd
    uart_callback.o(.text.bt_control) refers to dflti.o(.text) for __aeabi_i2d
    uart_callback.o(.text.bt_control) refers to pow.o(i.pow) for pow
    uart_callback.o(.text.bt_control) refers to f2d.o(.text) for __aeabi_f2d
    uart_callback.o(.text.bt_control) refers to dmul.o(.text) for __aeabi_dmul
    uart_callback.o(.text.bt_control) refers to dadd.o(.text) for __aeabi_dadd
    uart_callback.o(.text.bt_control) refers to d2f.o(.text) for __aeabi_d2f
    uart_callback.o(.text.bt_control) refers to fdiv.o(.text) for __aeabi_fdiv
    uart_callback.o(.text.bt_control) refers to memseta.o(.text) for __aeabi_memclr
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.Usart_Receive) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.Turn_Flag) for Turn_Flag
    uart_callback.o(.text.bt_control) refers to empty.o(.data.RC_Velocity) for RC_Velocity
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.Flag_PID) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.i) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.Receive) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.j) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.Data) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to ir_module.o(.data.minTurnAngle) for minTurnAngle
    uart_callback.o(.text.bt_control) refers to ir_module.o(.data.midTurnAngle) for midTurnAngle
    uart_callback.o(.text.bt_control) refers to ir_module.o(.data.maxTurnAngle) for maxTurnAngle
    uart_callback.o(.text.bt_control) refers to ir_module.o(.data.Turn90Angle) for Turn90Angle
    uart_callback.o(.text.bt_control) refers to control.o(.data.Velocity_KI) for Velocity_KI
    uart_callback.o(.text.bt_control) refers to control.o(.data.Velocity_KP) for Velocity_KP
    uart_callback.o(.text.bt_control) refers to empty.o(.bss.PID_Send) for PID_Send
    uart_callback.o(.ARM.exidx.text.bt_control) refers to uart_callback.o(.text.bt_control) for [Anonymous Symbol]
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.BT_DAMConfig) for BT_DAMConfig
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.bss.gBTCounts) for gBTCounts
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.bss.gBTPacket) for gBTPacket
    uart_callback.o(.ARM.exidx.text.UART1_IRQHandler) refers to uart_callback.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to uart_callback.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_UART_receiveData) refers to uart_callback.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to board.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to board.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to board.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to board.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_mspm0g350x_uvision.o(HEAP), (4096 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_VOLTAGE_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (52 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (52 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableLoopbackMode), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing empty.o(.bss.lap_count), (4 bytes).
    Removing empty.o(.bss.Motor_Left), (4 bytes).
    Removing empty.o(.bss.Motor_Right), (4 bytes).
    Removing empty.o(.bss.RC_Turn_Velocity), (4 bytes).
    Removing empty.o(.bss.Move_Y), (4 bytes).
    Removing empty.o(.bss.PS2_ON_Flag), (4 bytes).
    Removing empty.o(.bss.test_num), (2 bytes).
    Removing empty.o(.bss.show_cnt), (2 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing board.o(.text), (0 bytes).
    Removing board.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing board.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing board.o(.text.fputs), (80 bytes).
    Removing board.o(.ARM.exidx.text.fputs), (8 bytes).
    Removing board.o(.text.puts), (48 bytes).
    Removing board.o(.ARM.exidx.text.puts), (8 bytes).
    Removing board.o(.ARM.exidx.text.Systick_getTick), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing board.o(.text.delay_1us), (16 bytes).
    Removing board.o(.ARM.exidx.text.delay_1us), (8 bytes).
    Removing board.o(.text.delay_1ms), (16 bytes).
    Removing board.o(.ARM.exidx.text.delay_1ms), (8 bytes).
    Removing board.o(.rodata.str1.1), (2 bytes).
    Removing board.o(.bss.tick_ms), (4 bytes).
    Removing board.o(.bss.start_time), (4 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (52 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (36 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (36 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_disable), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (36 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (142 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setOversampling), (30 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setBaudRateDivisor), (76 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (64 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (74 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (32 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_isRXFIFOEmpty), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_isRXFIFOEmpty), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveData), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_isTXFIFOFull), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (58 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (70 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (70 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (200 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (228 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_enable), (22 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (256 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (276 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (88 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (128 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_sendCommand), (36 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_sendCommand), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (52 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setLoadValue), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCounterValueAfterEnable), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (280 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getInChanConfig), (150 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCCPDirection), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (180 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (176 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getInChanPairConfig), (82 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanPairConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (184 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (156 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (56 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (54 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (42 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (444 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (480 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (52 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (676 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (696 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (64 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.text.DL_SPI_init), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_setClockConfig), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_isRXFIFOEmpty), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_isRXFIFOEmpty), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_isTXFIFOFull), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_isTXFIFOFull), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData8), (22 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData16), (22 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (60 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (60 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (56 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (70 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (70 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (172 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (200 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_enable), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_enable), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (216 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setClockFormat), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary), (32 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (176 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getClockFormat), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary), (20 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (134 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (140 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (134 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (140 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary), (20 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (76 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_getGain), (32 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_getGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_setGain), (40 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_setGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (80 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.HW_RD_FIELD32_RAW), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_RD_FIELD32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.HW_WR_FIELD32_RAW), (50 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_WR_FIELD32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (388 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.HW_RD_REG32_RAW), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_RD_REG32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.HW_WR_REG32_RAW), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_WR_REG32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessUnlock), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessLock), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (300 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (116 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccLoadRegister), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccLoadRegister), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (324 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (680 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (180 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getMsgObjSize), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getMsgObjSize), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsg), (320 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (304 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsg), (382 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (248 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (116 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (140 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (100 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (160 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (104 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (100 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (196 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (136 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (136 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (92 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (252 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (104 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (92 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (156 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (148 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (192 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (576 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (696 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getDataSize), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getDataSize), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (64 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_setOperandTwo), (24 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandTwo), (8 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_setOperandOne), (24 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandOne), (8 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (84 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOFull), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitControllerData), (22 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitControllerData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerRXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (84 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOFull), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetData), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetRXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getTargetStatus), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getTargetStatus), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (60 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (34 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetData), (18 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (58 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setCommandAddress), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (64 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (84 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_waitForCmdDone), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getDATAFlashSize), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (378 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks), (60 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getNumBanks), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getMAINFlashSize), (8 bytes).
    Removing dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled), (4 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isFlashBankSwapEnabled), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_enableAddressOverrideMode), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setBankSelect), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setBankSelect), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setRegionSelect), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_disableAddressOverrideMode), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (60 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (148 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (348 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (146 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (158 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (156 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (176 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (178 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber), (14 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumber), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank), (82 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumberInBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank), (4 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isExecuteFromUpperFlashBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (356 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (68 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (84 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (104 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_CORE_getInstructionConfig), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_CORE_getInstructionConfig), (8 bytes).
    Removing dl_flashctl.o(.text.DL_CORE_configInstruction), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_CORE_configInstruction), (8 bytes).
    Removing dl_flashctl.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryConfig), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyConfig), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_configTransfer), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_setTrigger), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (120 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_configDataFormat), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_configDataFormat), (8 bytes).
    Removing dl_dac12.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_isFIFOFull), (34 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_isFIFOFull), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_output8), (22 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_output8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_output12), (28 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_output12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (70 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (72 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (32 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_startCalibration), (18 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_startCalibration), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_isCalibrationRunning), (30 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_isCalibrationRunning), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_getInterruptStatus), (24 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_getInterruptStatus), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (70 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_setSeed32), (24 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_feedData32), (24 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_feedData32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_getResult32), (20 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_getResult32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (64 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (82 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_setSeed16), (36 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_feedData16), (36 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_feedData16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_getResult16), (20 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_getResult16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (72 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (64 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_checkAlignmentAndReturnPointer), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (80 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataWord), (66 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataWord), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (58 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (98 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (86 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (132 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (132 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (76 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_interrupt.o(.text), (0 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_registerInterrupt), (92 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt), (8 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt), (28 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt), (8 bytes).
    Removing dl_interrupt.o(.vtable), (192 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Refresh_Gram), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WR_Byte), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RS_Set), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RS_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SCLK_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SDIN_Set), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SDIN_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SCLK_Set), (8 bytes).
    Removing oled.o(.text.OLED_Display_On), (34 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_On), (8 bytes).
    Removing oled.o(.text.OLED_Display_Off), (34 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_Off), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawPoint), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.ARM.exidx.text.oled_pow), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNumber), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RST_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RST_Set), (8 bytes).
    Removing oled.o(.text.OLED_ShowCHinese), (160 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowCHinese), (8 bytes).
    Removing oled.o(.text.OLED_Set_Pos), (60 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing oled.o(.rodata.Hzk16), (2624 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.ARM.exidx.text.keyValue), (8 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing key.o(.ARM.exidx.text.key_scan), (8 bytes).
    Removing key.o(.ARM.exidx.text.lapsKeyValue), (8 bytes).
    Removing key.o(.ARM.exidx.text.laps_key_scan), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.ARM.exidx.text.LED_ON), (8 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing led.o(.text.LED_OFF), (20 bytes).
    Removing led.o(.ARM.exidx.text.LED_OFF), (8 bytes).
    Removing led.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing led.o(.ARM.exidx.text.LED_Toggle), (8 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_togglePins), (8 bytes).
    Removing led.o(.ARM.exidx.text.LED_Flash), (8 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.ARM.exidx.text.Set_PWM), (8 bytes).
    Removing motor.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing motor.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing encoder.o(.text), (0 bytes).
    Removing encoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing adc.o(.text), (0 bytes).
    Removing adc.o(.ARM.exidx.text.Get_battery_volt), (8 bytes).
    Removing adc.o(.ARM.exidx.text.DL_ADC12_startConversion), (8 bytes).
    Removing adc.o(.ARM.exidx.text.DL_ADC12_getMemResult), (8 bytes).
    Removing adc.o(.ARM.exidx.text.ADC1_IRQHandler), (8 bytes).
    Removing adc.o(.ARM.exidx.text.DL_ADC12_getPendingInterrupt), (8 bytes).
    Removing ir_module.o(.text), (0 bytes).
    Removing ir_module.o(.ARM.exidx.text.IRDM_line_inspection), (8 bytes).
    Removing ir_module.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing control.o(.text), (0 bytes).
    Removing control.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing control.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing control.o(.ARM.exidx.text.Key), (8 bytes).
    Removing control.o(.ARM.exidx.text.Get_Velocity_From_Encoder), (8 bytes).
    Removing control.o(.ARM.exidx.text.Get_Target_Encoder), (8 bytes).
    Removing control.o(.ARM.exidx.text.Incremental_PI_Left), (8 bytes).
    Removing control.o(.ARM.exidx.text.Incremental_PI_Right), (8 bytes).
    Removing control.o(.ARM.exidx.text.myabs), (8 bytes).
    Removing control.o(.text.Turn_Off), (14 bytes).
    Removing control.o(.ARM.exidx.text.Turn_Off), (8 bytes).
    Removing control.o(.text.PWM_Limit), (60 bytes).
    Removing control.o(.ARM.exidx.text.PWM_Limit), (8 bytes).
    Removing control.o(.bss.CCD_count), (1 bytes).
    Removing control.o(.bss.ELE_count), (1 bytes).
    Removing control.o(.bss.Sensor_Left), (4 bytes).
    Removing control.o(.bss.Sensor_Middle), (4 bytes).
    Removing control.o(.bss.Sensor_Right), (4 bytes).
    Removing control.o(.bss.Sensor), (4 bytes).
    Removing show.o(.text), (0 bytes).
    Removing show.o(.ARM.exidx.text.oled_show), (8 bytes).
    Removing show.o(.ARM.exidx.text.APP_Show), (8 bytes).
    Removing show.o(.text.DataScope), (82 bytes).
    Removing show.o(.ARM.exidx.text.DataScope), (8 bytes).
    Removing datascope_dp.o(.text), (0 bytes).
    Removing datascope_dp.o(.text.Float2Byte), (64 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.Float2Byte), (8 bytes).
    Removing datascope_dp.o(.text.DataScope_Get_Channel_Data), (224 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.DataScope_Get_Channel_Data), (8 bytes).
    Removing datascope_dp.o(.text.DataScope_Data_Generate), (268 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.DataScope_Data_Generate), (8 bytes).
    Removing datascope_dp.o(.bss.DataScope_OutPut_Buffer), (42 bytes).
    Removing uart_callback.o(.text), (0 bytes).
    Removing uart_callback.o(.ARM.exidx.text.BT_DAMConfig), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_disableChannel), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setSrcAddr), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setDestAddr), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setTransferSize), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_enableChannel), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.BTBufferHandler), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_getTransferSize), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.bt_control), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing uart_callback.o(.bss.Flag_Direction), (4 bytes).
    Removing uart_callback.o(.bss.lastBTCounts), (1 bytes).
    Removing uart_callback.o(.bss.RecvOverFlag), (1 bytes).
    Removing uart_callback.o(.bss.Flag_Left), (4 bytes).
    Removing uart_callback.o(.bss.Flag_Right), (4 bytes).
    Removing uart_callback.o(.bss.gCheckBT), (1 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (84 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

1203 unused section(s) (total 46415 bytes) removed from the image.
