Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to adc.o(.text.ADC1_IRQHandler) for ADC1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_callback.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to control.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) for SYSCFG_DL_ADC12_VOLTAGE_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for DL_ADC12_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for DL_ADC12_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for DL_GPIO_setUpperPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for DL_UART_enableFIFOs
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for DL_UART_setRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for DL_UART_setTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode) for DL_UART_enableLoopbackMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for DL_UART_enableDMAReceiveEvent
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for DL_ADC12_initSingleSample
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for DL_ADC12_configConversionMem
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for DL_ADC12_setSampleTime0
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus) for DL_ADC12_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt) for DL_ADC12_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for DL_ADC12_enableConversions
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.rodata.gADC12_VOLTAGEClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for SYSCFG_DL_DMA_CH0_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for DL_SYSTICK_enable
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableLoopbackMode) refers to ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    empty.o(.text.main) refers to empty.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    empty.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    empty.o(.text.main) refers to lcd_init.o(.text.LCD_Init) for LCD_Init
    empty.o(.text.main) refers to lcd.o(.text.LCD_Display_Status) for LCD_Display_Status
    empty.o(.text.main) refers to adc.o(.text.Get_battery_volt) for Get_battery_volt
    empty.o(.text.main) refers to uart_callback.o(.text.BTBufferHandler) for BTBufferHandler
    empty.o(.text.main) refers to show.o(.text.oled_show) for oled_show
    empty.o(.text.main) refers to show.o(.text.APP_Show) for APP_Show
    empty.o(.text.main) refers to empty.o(.bss.Voltage) for Voltage
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to empty.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    board.o(.text.fputc) refers to board.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    board.o(.text.fputc) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    board.o(.ARM.exidx.text.fputc) refers to board.o(.text.fputc) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.DL_UART_isBusy) refers to board.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    board.o(.text.fputs) refers to strlen.o(.text) for strlen
    board.o(.text.fputs) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    board.o(.ARM.exidx.text.fputs) refers to board.o(.text.fputs) for [Anonymous Symbol]
    board.o(.text.puts) refers to board.o(.text.fputs) for fputs
    board.o(.text.puts) refers to stdout.o(.data) for __stdout
    board.o(.text.puts) refers to board.o(.rodata.str1.1) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.puts) refers to board.o(.text.puts) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.Systick_getTick) refers to board.o(.text.Systick_getTick) for [Anonymous Symbol]
    board.o(.text.delay_ms) refers to board.o(.text.delay_us) for delay_us
    board.o(.ARM.exidx.text.delay_ms) refers to board.o(.text.delay_ms) for [Anonymous Symbol]
    board.o(.text.delay_us) refers to board.o(.text.Systick_getTick) for Systick_getTick
    board.o(.ARM.exidx.text.delay_us) refers to board.o(.text.delay_us) for [Anonymous Symbol]
    board.o(.text.delay_1us) refers to board.o(.text.delay_us) for delay_us
    board.o(.ARM.exidx.text.delay_1us) refers to board.o(.text.delay_1us) for [Anonymous Symbol]
    board.o(.text.delay_1ms) refers to board.o(.text.delay_ms) for delay_ms
    board.o(.ARM.exidx.text.delay_1ms) refers to board.o(.text.delay_1ms) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_disable) for DL_UART_disable
    dl_uart.o(.text.DL_UART_init) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_disable) refers to dl_uart.o(.text.DL_UART_disable) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_uart.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setOversampling) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setOversampling) refers to dl_uart.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setBaudRateDivisor) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to dl_uart.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for DL_UART_setIrDAPulseLength
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_isRXFIFOEmpty) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveData) refers to dl_uart.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_isTXFIFOFull) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitData) refers to dl_uart.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_enable) for DL_UART_enable
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_enable) refers to dl_uart.o(.text.DL_UART_enable) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_enable) for DL_UART_enable
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_trng.o(.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_sendCommand) for DL_TRNG_sendCommand
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_trng.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_trng.o(.text.DL_TRNG_sendCommand) refers to dl_trng.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_trng.o(.ARM.exidx.text.DL_TRNG_sendCommand) refers to dl_trng.o(.text.DL_TRNG_sendCommand) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for DL_Timer_setCounterValueAfterEnable
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setLoadValue) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCounterValueAfterEnable) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanConfig) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_timer.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_getInChanPairConfig) for DL_Timer_getInChanPairConfig
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanPairConfig) refers to dl_timer.o(.text.DL_Timer_getInChanPairConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for DL_Timer_setCaptureCompareAction
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for DL_Timer_setCounterValueAfterEnable
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for DL_Timer_setCaptureCompareAction
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_init) refers to dl_spi.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_spi.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_isRXFIFOEmpty) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_isTXFIFOFull) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_enable) for DL_SPI_enable
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_enable) refers to dl_spi.o(.text.DL_SPI_enable) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat) for DL_RTC_Common_setClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary) for DL_RTC_Common_setCalendarSecondsBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary) for DL_RTC_Common_setCalendarMinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary) for DL_RTC_Common_setCalendarHoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary) for DL_RTC_Common_setCalendarMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary) for DL_RTC_Common_setCalendarYearBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD) for DL_RTC_Common_setCalendarSecondsBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD) for DL_RTC_Common_setCalendarMinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD) for DL_RTC_Common_setCalendarHoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD) for DL_RTC_Common_setCalendarMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD) for DL_RTC_Common_setCalendarYearBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setClockFormat) refers to dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary) for DL_RTC_Common_getCalendarSecondsBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary) for DL_RTC_Common_getCalendarMinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary) for DL_RTC_Common_getCalendarHoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary) for DL_RTC_Common_getCalendarDayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary) for DL_RTC_Common_getCalendarDayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary) for DL_RTC_Common_getCalendarMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary) for DL_RTC_Common_getCalendarYearBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD) for DL_RTC_Common_getCalendarSecondsBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD) for DL_RTC_Common_getCalendarMinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD) for DL_RTC_Common_getCalendarHoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD) for DL_RTC_Common_getCalendarDayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD) for DL_RTC_Common_getCalendarDayOfMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD) for DL_RTC_Common_getCalendarMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD) for DL_RTC_Common_getCalendarYearBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getClockFormat) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary) for DL_RTC_Common_setAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary) for DL_RTC_Common_setAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) for DL_RTC_Common_setAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) for DL_RTC_Common_setAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD) for DL_RTC_Common_setAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD) for DL_RTC_Common_setAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) for DL_RTC_Common_setAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) for DL_RTC_Common_setAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary) for DL_RTC_Common_getAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary) for DL_RTC_Common_getAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) for DL_RTC_Common_getAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) for DL_RTC_Common_getAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD) for DL_RTC_Common_getAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD) for DL_RTC_Common_getAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) for DL_RTC_Common_getAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) for DL_RTC_Common_getAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary) for DL_RTC_Common_enableAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary) for DL_RTC_Common_enableAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) for DL_RTC_Common_enableAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) for DL_RTC_Common_enableAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD) for DL_RTC_Common_enableAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD) for DL_RTC_Common_enableAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) for DL_RTC_Common_enableAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) for DL_RTC_Common_enableAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary) for DL_RTC_Common_disableAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary) for DL_RTC_Common_disableAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) for DL_RTC_Common_disableAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) for DL_RTC_Common_disableAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD) for DL_RTC_Common_disableAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD) for DL_RTC_Common_disableAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) for DL_RTC_Common_disableAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) for DL_RTC_Common_disableAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary) for DL_RTC_Common_setAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary) for DL_RTC_Common_setAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) for DL_RTC_Common_setAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) for DL_RTC_Common_setAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD) for DL_RTC_Common_setAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD) for DL_RTC_Common_setAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) for DL_RTC_Common_setAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) for DL_RTC_Common_setAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary) for DL_RTC_Common_getAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary) for DL_RTC_Common_getAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) for DL_RTC_Common_getAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) for DL_RTC_Common_getAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD) for DL_RTC_Common_getAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD) for DL_RTC_Common_getAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) for DL_RTC_Common_getAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) for DL_RTC_Common_getAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary) for DL_RTC_Common_enableAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary) for DL_RTC_Common_enableAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) for DL_RTC_Common_enableAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) for DL_RTC_Common_enableAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD) for DL_RTC_Common_enableAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD) for DL_RTC_Common_enableAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) for DL_RTC_Common_enableAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) for DL_RTC_Common_enableAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary) for DL_RTC_Common_disableAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary) for DL_RTC_Common_disableAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) for DL_RTC_Common_disableAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) for DL_RTC_Common_disableAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD) for DL_RTC_Common_disableAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD) for DL_RTC_Common_disableAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) for DL_RTC_Common_disableAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) for DL_RTC_Common_disableAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_getGain) for DL_OPA_getGain
    dl_opa.o(.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_setGain) for DL_OPA_setGain
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_getGain) refers to dl_opa.o(.text.DL_OPA_getGain) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_setGain) refers to dl_opa.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_opa.o(.ARM.exidx.text.DL_OPA_setGain) refers to dl_opa.o(.text.DL_OPA_setGain) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_getGain) for DL_OPA_getGain
    dl_opa.o(.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_setGain) for DL_OPA_setGain
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_opa.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_mcan.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_RD_FIELD32_RAW) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_WR_FIELD32_RAW) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_RD_REG32_RAW) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_WR_REG32_RAW) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessUnlock) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessLock) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for DL_MCAN_getMsgObjSize
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsg) for DL_MCAN_writeMsg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getMsgObjSize) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getMsgObjSize) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for DL_MCAN_getDataSize
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.DL_MCAN_writeMsg) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for DL_MCAN_getMsgObjSize
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsg) for DL_MCAN_readMsg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for DL_MCAN_getDataSize
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.DL_MCAN_readMsg) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getDataSize) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getDataSize) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for [Anonymous Symbol]
    dl_mathacl.o(.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_setOperandTwo) for DL_MathACL_setOperandTwo
    dl_mathacl.o(.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_setOperandOne) for DL_MathACL_setOperandOne
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandTwo) refers to dl_mathacl.o(.text.DL_MathACL_setOperandTwo) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandOne) refers to dl_mathacl.o(.text.DL_MathACL_setOperandOne) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_i2c.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull) for DL_I2C_isControllerTXFIFOFull
    dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_transmitControllerData) for DL_I2C_transmitControllerData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOFull) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitControllerData) refers to dl_i2c.o(.text.DL_I2C_transmitControllerData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO) for DL_I2C_startFlushControllerTXFIFO
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty) for DL_I2C_isControllerTXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO) for DL_I2C_stopFlushControllerTXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO) for DL_I2C_startFlushControllerRXFIFO
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty) for DL_I2C_isControllerRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO) for DL_I2C_stopFlushControllerRXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerRXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for DL_I2C_isTargetTXFIFOFull
    dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOFull) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetData) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO) for DL_I2C_startFlushTargetTXFIFO
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty) for DL_I2C_isTargetTXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO) for DL_I2C_stopFlushTargetTXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO) for DL_I2C_startFlushTargetRXFIFO
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for DL_I2C_isTargetRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO) for DL_I2C_stopFlushTargetRXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetRXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for DL_I2C_getTargetStatus
    dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getTargetStatus) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for DL_I2C_isTargetTXFIFOFull
    dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for DL_I2C_getTargetStatus
    dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for DL_I2C_receiveTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetData) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for DL_I2C_isTargetRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for DL_I2C_receiveTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setCommandAddress) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_waitForCmdDone) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for DL_FlashCTL_unprotectDataMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for DL_FlashCTL_eraseDataBankFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for DL_FlashCTL_unprotectDataMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled) for DL_SYSCTL_isFlashBankSwapEnabled
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode) for DL_FlashCTL_enableAddressOverrideMode
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) for DL_FlashCTL_setBankSelect
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) for DL_FlashCTL_setRegionSelect
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode) for DL_FlashCTL_disableAddressOverrideMode
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isFlashBankSwapEnabled) refers to dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_enableAddressOverrideMode) refers to dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) refers to dl_flashctl.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setBankSelect) refers to dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) refers to dl_flashctl.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setRegionSelect) refers to dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_disableAddressOverrideMode) refers to dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for DL_FlashCTL_massErase
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for DL_FlashCTL_massEraseFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for DL_FlashCTL_programMemory64WithECCGenerated
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for DL_FlashCTL_getFlashSectorNumberInBank
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for DL_SYSCTL_isExecuteFromUpperFlashBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for DL_FlashCTL_programMemoryFromRAM64WithECCGenerated
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for DL_FlashCTL_programMemory64WithECCManual
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for DL_FlashCTL_programMemoryFromRAM64WithECCManual
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for DL_FlashCTL_programMemory32
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for DL_FlashCTL_programMemory64
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for DL_FlashCTL_programMemoryFromRAM32
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for DL_FlashCTL_programMemoryFromRAM64
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumber) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isExecuteFromUpperFlashBank) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for DL_FlashCTL_getFlashSectorNumberInBank
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for DL_SYSCTL_isExecuteFromUpperFlashBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_CORE_getInstructionConfig) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_CORE_configInstruction) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_flashctl.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for [Anonymous Symbol]
    dl_dma.o(.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_configTransfer) for DL_DMA_configTransfer
    dl_dma.o(.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_setTrigger) for DL_DMA_setTrigger
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_configTransfer) refers to dl_dma.o(.text.DL_DMA_configTransfer) for [Anonymous Symbol]
    dl_dma.o(.text.DL_DMA_setTrigger) refers to dl_dma.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dma.o(.ARM.exidx.text.DL_DMA_setTrigger) refers to dl_dma.o(.text.DL_DMA_setTrigger) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_dma.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_configDataFormat) for DL_DAC12_configDataFormat
    dl_dac12.o(.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_configDataFormat) refers to dl_dac12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_configDataFormat) refers to dl_dac12.o(.text.DL_DAC12_configDataFormat) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_dac12.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_output8) for DL_DAC12_output8
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_isFIFOFull) refers to dl_dac12.o(.text.DL_DAC12_getInterruptStatus) for DL_DAC12_getInterruptStatus
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_isFIFOFull) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_output8) refers to dl_dac12.o(.text.DL_DAC12_output8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_output12) for DL_DAC12_output12
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_output12) refers to dl_dac12.o(.text.DL_DAC12_output12) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_output8) for DL_DAC12_output8
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_output12) for DL_DAC12_output12
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_startCalibration) for DL_DAC12_startCalibration
    dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_isCalibrationRunning) for DL_DAC12_isCalibrationRunning
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_startCalibration) refers to dl_dac12.o(.text.DL_DAC12_startCalibration) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_isCalibrationRunning) refers to dl_dac12.o(.text.DL_DAC12_isCalibrationRunning) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_getInterruptStatus) refers to dl_dac12.o(.text.DL_DAC12_getInterruptStatus) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for DL_CRC_setSeed32
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_feedData32) for DL_CRC_feedData32
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_getResult32) for DL_CRC_getResult32
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_feedData32) refers to dl_crc.o(.text.DL_CRC_feedData32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_getResult32) refers to dl_crc.o(.text.DL_CRC_getResult32) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for DL_CRC_setSeed32
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_feedData32) for DL_CRC_feedData32
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_getResult32) for DL_CRC_getResult32
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for DL_CRC_setSeed16
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_feedData16) for DL_CRC_feedData16
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_getResult16) for DL_CRC_getResult16
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_feedData16) refers to dl_crc.o(.text.DL_CRC_feedData16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_getResult16) refers to dl_crc.o(.text.DL_CRC_getResult16) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for DL_CRC_setSeed16
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_feedData16) for DL_CRC_feedData16
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_getResult16) for DL_CRC_getResult16
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for DL_AES_setKeyAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_checkAlignmentAndReturnPointer) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataWord) refers to dl_aes.o(.text.DL_AES_loadDataWord) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for DL_AES_loadDataInAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for DL_AES_getDataOutAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for DL_AES_loadXORDataInAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for DL_AES_loadXORDataInWithoutTriggerAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for DL_AES_xorDataAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_adc12.o(.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_adc12.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to startup_mspm0g350x_uvision.o(.text) for Default_Handler
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) for [Anonymous Symbol]
    oled.o(.text.OLED_Refresh_Gram) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Refresh_Gram) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Refresh_Gram) refers to oled.o(.text.OLED_Refresh_Gram) for [Anonymous Symbol]
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_RS_Set) for OLED_RS_Set
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_RS_Clr) for OLED_RS_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SCLK_Clr) for OLED_SCLK_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SDIN_Set) for OLED_SDIN_Set
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SDIN_Clr) for OLED_SDIN_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SCLK_Set) for OLED_SCLK_Set
    oled.o(.ARM.exidx.text.OLED_WR_Byte) refers to oled.o(.text.OLED_WR_Byte) for [Anonymous Symbol]
    oled.o(.text.OLED_RS_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_RS_Set) refers to oled.o(.text.OLED_RS_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_RS_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_RS_Clr) refers to oled.o(.text.OLED_RS_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SCLK_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_SCLK_Clr) refers to oled.o(.text.OLED_SCLK_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SDIN_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_SDIN_Set) refers to oled.o(.text.OLED_SDIN_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_SDIN_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_SDIN_Clr) refers to oled.o(.text.OLED_SDIN_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SCLK_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_SCLK_Set) refers to oled.o(.text.OLED_SCLK_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_On) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Display_On) refers to oled.o(.text.OLED_Display_On) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_Off) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Display_Off) refers to oled.o(.text.OLED_Display_Off) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.text.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(.text.OLED_Clear) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawPoint) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_DrawPoint) refers to oled.o(.text.OLED_DrawPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.oled_asc2_1608) for oled_asc2_1608
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.oled_asc2_1206) for oled_asc2_1206
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.oled_pow) refers to oled.o(.text.oled_pow) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNumber) refers to oled.o(.text.oled_pow) for oled_pow
    oled.o(.text.OLED_ShowNumber) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    oled.o(.text.OLED_ShowNumber) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowNumber) refers to oled.o(.text.OLED_ShowNumber) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_RST_Clr) for OLED_RST_Clr
    oled.o(.text.OLED_Init) refers to board.o(.text.delay_ms) for delay_ms
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_RST_Set) for OLED_RST_Set
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_RST_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_RST_Clr) refers to oled.o(.text.OLED_RST_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_RST_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_RST_Set) refers to oled.o(.text.OLED_RST_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.rodata.Hzk16) for Hzk16
    oled.o(.ARM.exidx.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_ShowCHinese) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Pos) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to oled.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_setPins) refers to oled.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.text.keyValue) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.ARM.exidx.text.keyValue) refers to key.o(.text.keyValue) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to ffltui.o(.text) for __aeabi_ui2f
    key.o(.text.key_scan) refers to fdiv.o(.text) for __aeabi_fdiv
    key.o(.text.key_scan) refers to fmul.o(.text) for __aeabi_fmul
    key.o(.text.key_scan) refers to key.o(.text.keyValue) for keyValue
    key.o(.text.key_scan) refers to ffixi.o(.text) for __aeabi_f2iz
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.check_once) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.press_flag) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.time_core) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.long_press_time) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.key_scan) refers to key.o(.text.key_scan) for [Anonymous Symbol]
    key.o(.text.lapsKeyValue) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.ARM.exidx.text.lapsKeyValue) refers to key.o(.text.lapsKeyValue) for [Anonymous Symbol]
    key.o(.text.laps_key_scan) refers to ffltui.o(.text) for __aeabi_ui2f
    key.o(.text.laps_key_scan) refers to fdiv.o(.text) for __aeabi_fdiv
    key.o(.text.laps_key_scan) refers to fmul.o(.text) for __aeabi_fmul
    key.o(.text.laps_key_scan) refers to key.o(.text.lapsKeyValue) for lapsKeyValue
    key.o(.text.laps_key_scan) refers to ffixi.o(.text) for __aeabi_f2iz
    key.o(.text.laps_key_scan) refers to key.o(.bss.laps_key_scan.check_once) for [Anonymous Symbol]
    key.o(.text.laps_key_scan) refers to key.o(.bss.laps_key_scan.press_flag) for [Anonymous Symbol]
    key.o(.text.laps_key_scan) refers to key.o(.bss.laps_key_scan.time_core) for [Anonymous Symbol]
    key.o(.text.laps_key_scan) refers to key.o(.bss.laps_key_scan.long_press_time) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.laps_key_scan) refers to key.o(.text.laps_key_scan) for [Anonymous Symbol]
    led.o(.text.LED_ON) refers to led.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    led.o(.ARM.exidx.text.LED_ON) refers to led.o(.text.LED_ON) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to led.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    led.o(.text.LED_OFF) refers to led.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    led.o(.ARM.exidx.text.LED_OFF) refers to led.o(.text.LED_OFF) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_setPins) refers to led.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    led.o(.text.LED_Toggle) refers to led.o(.text.DL_GPIO_togglePins) for DL_GPIO_togglePins
    led.o(.ARM.exidx.text.LED_Toggle) refers to led.o(.text.LED_Toggle) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_togglePins) refers to led.o(.text.DL_GPIO_togglePins) for [Anonymous Symbol]
    led.o(.text.LED_Flash) refers to led.o(.text.LED_ON) for LED_ON
    led.o(.text.LED_Flash) refers to led.o(.text.LED_Toggle) for LED_Toggle
    led.o(.text.LED_Flash) refers to led.o(.bss.LED_Flash.temp) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.LED_Flash) refers to led.o(.text.LED_Flash) for [Anonymous Symbol]
    motor.o(.text.Set_PWM) refers to motor.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor.o(.text.Set_PWM) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.text.Set_PWM) refers to motor.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    motor.o(.ARM.exidx.text.Set_PWM) refers to motor.o(.text.Set_PWM) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to motor.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.DL_GPIO_setPins) refers to motor.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.gpio_interrup1) for gpio_interrup1
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.gpio_interrup2) for gpio_interrup2
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countA) for Get_Encoder_countA
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countB) for Get_Encoder_countB
    encoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_readPins) refers to encoder.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to encoder.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    adc.o(.text.Get_battery_volt) refers to adc.o(.text.DL_ADC12_startConversion) for DL_ADC12_startConversion
    adc.o(.text.Get_battery_volt) refers to adc.o(.text.DL_ADC12_getMemResult) for DL_ADC12_getMemResult
    adc.o(.text.Get_battery_volt) refers to dflti.o(.text) for __aeabi_i2d
    adc.o(.text.Get_battery_volt) refers to dmul.o(.text) for __aeabi_dmul
    adc.o(.text.Get_battery_volt) refers to ddiv.o(.text) for __aeabi_ddiv
    adc.o(.text.Get_battery_volt) refers to d2f.o(.text) for __aeabi_d2f
    adc.o(.text.Get_battery_volt) refers to adc.o(.bss.gCheckADC) for gCheckADC
    adc.o(.ARM.exidx.text.Get_battery_volt) refers to adc.o(.text.Get_battery_volt) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.DL_ADC12_startConversion) refers to adc.o(.text.DL_ADC12_startConversion) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.DL_ADC12_getMemResult) refers to adc.o(.text.DL_ADC12_getMemResult) for [Anonymous Symbol]
    adc.o(.text.ADC1_IRQHandler) refers to adc.o(.text.DL_ADC12_getPendingInterrupt) for DL_ADC12_getPendingInterrupt
    adc.o(.text.ADC1_IRQHandler) refers to adc.o(.bss.gCheckADC) for gCheckADC
    adc.o(.ARM.exidx.text.ADC1_IRQHandler) refers to adc.o(.text.ADC1_IRQHandler) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.DL_ADC12_getPendingInterrupt) refers to adc.o(.text.DL_ADC12_getPendingInterrupt) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to fdiv.o(.text) for __aeabi_fdiv
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    ir_module.o(.text.IRDM_line_inspection) refers to fmul.o(.text) for __aeabi_fmul
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.data.RC_Velocity) for RC_Velocity
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.ir_dh4_state) for ir_dh4_state
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.ir_dh3_state) for ir_dh3_state
    ir_module.o(.text.IRDM_line_inspection) refers to lcd.o(.text.LCD_Display_Status) for LCD_Display_Status
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.ir_dh2_state) for ir_dh2_state
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.ir_dh1_state) for ir_dh1_state
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.turn_cooldown) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.corner_detect_delay) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.turn_phase) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.bss.Move_X) for Move_X
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.bss.Move_Z) for Move_Z
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.turn_counter) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.last_state) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.bss.IRDM_line_inspection.ten_time) for [Anonymous Symbol]
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.data.minTurnAngle) for minTurnAngle
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.data.midTurnAngle) for midTurnAngle
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.data.sharpTurnAngle) for sharpTurnAngle
    ir_module.o(.text.IRDM_line_inspection) refers to ir_module.o(.data.maxTurnAngle) for maxTurnAngle
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.bss.corner_count) for corner_count
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.bss.current_laps) for current_laps
    ir_module.o(.text.IRDM_line_inspection) refers to empty.o(.data.target_laps) for target_laps
    ir_module.o(.text.IRDM_line_inspection) refers to control.o(.data.Flag_Stop) for Flag_Stop
    ir_module.o(.ARM.exidx.text.IRDM_line_inspection) refers to ir_module.o(.text.IRDM_line_inspection) for [Anonymous Symbol]
    ir_module.o(.ARM.exidx.text.DL_GPIO_readPins) refers to ir_module.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    lcd.o(.text.LCD_Fill) refers to lcd_init.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd.o(.text.LCD_Fill) refers to lcd_init.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(.ARM.exidx.text.LCD_Fill) refers to lcd.o(.text.LCD_Fill) for [Anonymous Symbol]
    lcd.o(.text.LCD_DrawPoint) refers to lcd_init.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd.o(.text.LCD_DrawPoint) refers to lcd_init.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(.ARM.exidx.text.LCD_DrawPoint) refers to lcd.o(.text.LCD_DrawPoint) for [Anonymous Symbol]
    lcd.o(.text.LCD_DrawLine) refers to lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(.ARM.exidx.text.LCD_DrawLine) refers to lcd.o(.text.LCD_DrawLine) for [Anonymous Symbol]
    lcd.o(.text.LCD_DrawRectangle) refers to lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd.o(.ARM.exidx.text.LCD_DrawRectangle) refers to lcd.o(.text.LCD_DrawRectangle) for [Anonymous Symbol]
    lcd.o(.text.Draw_Circle) refers to lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(.ARM.exidx.text.Draw_Circle) refers to lcd.o(.text.Draw_Circle) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowChinese) refers to lcd.o(.text.LCD_ShowChinese12x12) for LCD_ShowChinese12x12
    lcd.o(.text.LCD_ShowChinese) refers to lcd.o(.text.LCD_ShowChinese16x16) for LCD_ShowChinese16x16
    lcd.o(.text.LCD_ShowChinese) refers to lcd.o(.text.LCD_ShowChinese24x24) for LCD_ShowChinese24x24
    lcd.o(.text.LCD_ShowChinese) refers to lcd.o(.text.LCD_ShowChinese32x32) for LCD_ShowChinese32x32
    lcd.o(.ARM.exidx.text.LCD_ShowChinese) refers to lcd.o(.text.LCD_ShowChinese) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowChinese12x12) refers to lcd_init.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd.o(.text.LCD_ShowChinese12x12) refers to lcd_init.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(.text.LCD_ShowChinese12x12) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    lcd.o(.text.LCD_ShowChinese12x12) refers to lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(.text.LCD_ShowChinese12x12) refers to lcd.o(.rodata.tfont12) for tfont12
    lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12) refers to lcd.o(.text.LCD_ShowChinese12x12) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowChinese16x16) refers to lcd_init.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd.o(.text.LCD_ShowChinese16x16) refers to lcd_init.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(.text.LCD_ShowChinese16x16) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    lcd.o(.text.LCD_ShowChinese16x16) refers to lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(.text.LCD_ShowChinese16x16) refers to lcd.o(.rodata.tfont16) for tfont16
    lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16) refers to lcd.o(.text.LCD_ShowChinese16x16) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowChinese24x24) refers to lcd_init.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd.o(.text.LCD_ShowChinese24x24) refers to lcd_init.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(.text.LCD_ShowChinese24x24) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    lcd.o(.text.LCD_ShowChinese24x24) refers to lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(.text.LCD_ShowChinese24x24) refers to lcd.o(.rodata.tfont24) for tfont24
    lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24) refers to lcd.o(.text.LCD_ShowChinese24x24) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowChinese32x32) refers to lcd_init.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd.o(.text.LCD_ShowChinese32x32) refers to lcd_init.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(.text.LCD_ShowChinese32x32) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    lcd.o(.text.LCD_ShowChinese32x32) refers to lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(.text.LCD_ShowChinese32x32) refers to lcd.o(.rodata.tfont32) for tfont32
    lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32) refers to lcd.o(.text.LCD_ShowChinese32x32) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowChar) refers to lcd_init.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd.o(.text.LCD_ShowChar) refers to lcd_init.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(.text.LCD_ShowChar) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    lcd.o(.text.LCD_ShowChar) refers to lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(.text.LCD_ShowChar) refers to lcd.o(.rodata.ascii_3216) for ascii_3216
    lcd.o(.text.LCD_ShowChar) refers to lcd.o(.rodata.ascii_2412) for ascii_2412
    lcd.o(.text.LCD_ShowChar) refers to lcd.o(.rodata.ascii_1608) for ascii_1608
    lcd.o(.text.LCD_ShowChar) refers to lcd.o(.rodata.ascii_1206) for ascii_1206
    lcd.o(.ARM.exidx.text.LCD_ShowChar) refers to lcd.o(.text.LCD_ShowChar) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowString) refers to lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    lcd.o(.ARM.exidx.text.LCD_ShowString) refers to lcd.o(.text.LCD_ShowString) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.mypow) refers to lcd.o(.text.mypow) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowIntNum) refers to lcd.o(.text.mypow) for mypow
    lcd.o(.text.LCD_ShowIntNum) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    lcd.o(.text.LCD_ShowIntNum) refers to lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    lcd.o(.ARM.exidx.text.LCD_ShowIntNum) refers to lcd.o(.text.LCD_ShowIntNum) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowFloatNum1) refers to fmul.o(.text) for __aeabi_fmul
    lcd.o(.text.LCD_ShowFloatNum1) refers to ffixi.o(.text) for __aeabi_f2iz
    lcd.o(.text.LCD_ShowFloatNum1) refers to lcd.o(.text.mypow) for mypow
    lcd.o(.text.LCD_ShowFloatNum1) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    lcd.o(.text.LCD_ShowFloatNum1) refers to lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1) refers to lcd.o(.text.LCD_ShowFloatNum1) for [Anonymous Symbol]
    lcd.o(.text.LCD_ShowPicture) refers to lcd_init.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd.o(.text.LCD_ShowPicture) refers to lcd_init.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    lcd.o(.ARM.exidx.text.LCD_ShowPicture) refers to lcd.o(.text.LCD_ShowPicture) for [Anonymous Symbol]
    lcd.o(.text.LCD_Display_Status) refers to lcd.o(.text.LCD_Fill) for LCD_Fill
    lcd.o(.text.LCD_Display_Status) refers to lcd.o(.text.LCD_ShowString) for LCD_ShowString
    lcd.o(.text.LCD_Display_Status) refers to lcd.o(.text.LCD_ShowIntNum) for LCD_ShowIntNum
    lcd.o(.text.LCD_Display_Status) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    lcd.o(.text.LCD_Display_Status) refers to lcd.o(.text.LCD_DrawRectangle) for LCD_DrawRectangle
    lcd.o(.text.LCD_Display_Status) refers to lcd.o(.rodata.str1.1) for [Anonymous Symbol]
    lcd.o(.text.LCD_Display_Status) refers to empty.o(.data.target_laps) for target_laps
    lcd.o(.text.LCD_Display_Status) refers to empty.o(.bss.current_laps) for current_laps
    lcd.o(.text.LCD_Display_Status) refers to empty.o(.bss.corner_count) for corner_count
    lcd.o(.text.LCD_Display_Status) refers to control.o(.data.Flag_Stop) for Flag_Stop
    lcd.o(.ARM.exidx.text.LCD_Display_Status) refers to lcd.o(.text.LCD_Display_Status) for [Anonymous Symbol]
    lcd_init.o(.text.LCD_GPIO_Init) refers to lcd_init.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lcd_init.o(.ARM.exidx.text.LCD_GPIO_Init) refers to lcd_init.o(.text.LCD_GPIO_Init) for [Anonymous Symbol]
    lcd_init.o(.ARM.exidx.text.DL_GPIO_setPins) refers to lcd_init.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    lcd_init.o(.text.LCD_Writ_Bus) refers to lcd_init.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    lcd_init.o(.text.LCD_Writ_Bus) refers to lcd_init.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lcd_init.o(.ARM.exidx.text.LCD_Writ_Bus) refers to lcd_init.o(.text.LCD_Writ_Bus) for [Anonymous Symbol]
    lcd_init.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to lcd_init.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    lcd_init.o(.text.LCD_WR_DATA8) refers to lcd_init.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init.o(.ARM.exidx.text.LCD_WR_DATA8) refers to lcd_init.o(.text.LCD_WR_DATA8) for [Anonymous Symbol]
    lcd_init.o(.text.LCD_WR_DATA) refers to lcd_init.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init.o(.ARM.exidx.text.LCD_WR_DATA) refers to lcd_init.o(.text.LCD_WR_DATA) for [Anonymous Symbol]
    lcd_init.o(.text.LCD_WR_REG) refers to lcd_init.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    lcd_init.o(.text.LCD_WR_REG) refers to lcd_init.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init.o(.text.LCD_WR_REG) refers to lcd_init.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lcd_init.o(.ARM.exidx.text.LCD_WR_REG) refers to lcd_init.o(.text.LCD_WR_REG) for [Anonymous Symbol]
    lcd_init.o(.text.LCD_Address_Set) refers to lcd_init.o(.text.LCD_WR_REG) for LCD_WR_REG
    lcd_init.o(.text.LCD_Address_Set) refers to lcd_init.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_init.o(.ARM.exidx.text.LCD_Address_Set) refers to lcd_init.o(.text.LCD_Address_Set) for [Anonymous Symbol]
    lcd_init.o(.text.LCD_Init) refers to lcd_init.o(.text.LCD_GPIO_Init) for LCD_GPIO_Init
    lcd_init.o(.text.LCD_Init) refers to lcd_init.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    lcd_init.o(.text.LCD_Init) refers to board.o(.text.delay_ms) for delay_ms
    lcd_init.o(.text.LCD_Init) refers to lcd_init.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    lcd_init.o(.text.LCD_Init) refers to lcd_init.o(.text.LCD_WR_REG) for LCD_WR_REG
    lcd_init.o(.text.LCD_Init) refers to lcd_init.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    lcd_init.o(.ARM.exidx.text.LCD_Init) refers to lcd_init.o(.text.LCD_Init) for [Anonymous Symbol]
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Key) for Key
    control.o(.text.TIMG0_IRQHandler) refers to led.o(.text.LED_Flash) for LED_Flash
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Get_Velocity_From_Encoder) for Get_Velocity_From_Encoder
    control.o(.text.TIMG0_IRQHandler) refers to ir_module.o(.text.IRDM_line_inspection) for IRDM_line_inspection
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Get_Target_Encoder) for Get_Target_Encoder
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Incremental_PI_Left) for Incremental_PI_Left
    control.o(.text.TIMG0_IRQHandler) refers to fflti.o(.text) for __aeabi_i2f
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.text.Incremental_PI_Right) for Incremental_PI_Right
    control.o(.text.TIMG0_IRQHandler) refers to f2d.o(.text) for __aeabi_f2d
    control.o(.text.TIMG0_IRQHandler) refers to printfa.o(i.__0printf) for printf
    control.o(.text.TIMG0_IRQHandler) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text.TIMG0_IRQHandler) refers to motor.o(.text.Set_PWM) for Set_PWM
    control.o(.text.TIMG0_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countA) for Get_Encoder_countA
    control.o(.text.TIMG0_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countB) for Get_Encoder_countB
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.data.Flag_Stop) for Flag_Stop
    control.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.Move_X) for Move_X
    control.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.Move_Z) for Move_Z
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.bss.MotorA) for MotorA
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.bss.MotorB) for MotorB
    control.o(.text.TIMG0_IRQHandler) refers to control.o(.rodata.str1.1) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to control.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to control.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    control.o(.text.Key) refers to key.o(.text.key_scan) for key_scan
    control.o(.text.Key) refers to lcd.o(.text.LCD_Display_Status) for LCD_Display_Status
    control.o(.text.Key) refers to key.o(.text.laps_key_scan) for laps_key_scan
    control.o(.text.Key) refers to control.o(.data.Flag_Stop) for Flag_Stop
    control.o(.text.Key) refers to empty.o(.bss.current_laps) for current_laps
    control.o(.text.Key) refers to empty.o(.bss.corner_count) for corner_count
    control.o(.text.Key) refers to empty.o(.data.target_laps) for target_laps
    control.o(.ARM.exidx.text.Key) refers to control.o(.text.Key) for [Anonymous Symbol]
    control.o(.text.Get_Velocity_From_Encoder) refers to fflti.o(.text) for __aeabi_i2f
    control.o(.text.Get_Velocity_From_Encoder) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text.Get_Velocity_From_Encoder) refers to f2d.o(.text) for __aeabi_f2d
    control.o(.text.Get_Velocity_From_Encoder) refers to dmul.o(.text) for __aeabi_dmul
    control.o(.text.Get_Velocity_From_Encoder) refers to ddiv.o(.text) for __aeabi_ddiv
    control.o(.text.Get_Velocity_From_Encoder) refers to d2f.o(.text) for __aeabi_d2f
    control.o(.text.Get_Velocity_From_Encoder) refers to control.o(.bss.OriginalEncoder) for OriginalEncoder
    control.o(.text.Get_Velocity_From_Encoder) refers to control.o(.bss.MotorA) for MotorA
    control.o(.text.Get_Velocity_From_Encoder) refers to control.o(.bss.MotorB) for MotorB
    control.o(.ARM.exidx.text.Get_Velocity_From_Encoder) refers to control.o(.text.Get_Velocity_From_Encoder) for [Anonymous Symbol]
    control.o(.text.Get_Target_Encoder) refers to fcmpge.o(.text) for __aeabi_fcmpge
    control.o(.text.Get_Target_Encoder) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text.Get_Target_Encoder) refers to fdiv.o(.text) for __aeabi_fdiv
    control.o(.text.Get_Target_Encoder) refers to fadd.o(.text) for __aeabi_fsub
    control.o(.text.Get_Target_Encoder) refers to control.o(.bss.MotorA) for MotorA
    control.o(.text.Get_Target_Encoder) refers to control.o(.bss.MotorB) for MotorB
    control.o(.ARM.exidx.text.Get_Target_Encoder) refers to control.o(.text.Get_Target_Encoder) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Left) refers to fadd.o(.text) for __aeabi_fsub
    control.o(.text.Incremental_PI_Left) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text.Incremental_PI_Left) refers to fcmple.o(.text) for __aeabi_fcmple
    control.o(.text.Incremental_PI_Left) refers to fcmpge.o(.text) for __aeabi_fcmpge
    control.o(.text.Incremental_PI_Left) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text.Incremental_PI_Left) refers to control.o(.bss.Incremental_PI_Left.Bias) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Left) refers to control.o(.data.Velocity_KP) for Velocity_KP
    control.o(.text.Incremental_PI_Left) refers to control.o(.bss.Incremental_PI_Left.Last_bias) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Left) refers to control.o(.data.Velocity_KI) for Velocity_KI
    control.o(.text.Incremental_PI_Left) refers to control.o(.bss.Incremental_PI_Left.Pwm) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.Incremental_PI_Left) refers to control.o(.text.Incremental_PI_Left) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Right) refers to fadd.o(.text) for __aeabi_fsub
    control.o(.text.Incremental_PI_Right) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.text.Incremental_PI_Right) refers to fcmple.o(.text) for __aeabi_fcmple
    control.o(.text.Incremental_PI_Right) refers to fcmpge.o(.text) for __aeabi_fcmpge
    control.o(.text.Incremental_PI_Right) refers to ffixi.o(.text) for __aeabi_f2iz
    control.o(.text.Incremental_PI_Right) refers to control.o(.bss.Incremental_PI_Right.Bias) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Right) refers to control.o(.data.Velocity_KP) for Velocity_KP
    control.o(.text.Incremental_PI_Right) refers to control.o(.bss.Incremental_PI_Right.Last_bias) for [Anonymous Symbol]
    control.o(.text.Incremental_PI_Right) refers to control.o(.data.Velocity_KI) for Velocity_KI
    control.o(.text.Incremental_PI_Right) refers to control.o(.bss.Incremental_PI_Right.Pwm) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.Incremental_PI_Right) refers to control.o(.text.Incremental_PI_Right) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.myabs) refers to control.o(.text.myabs) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.Turn_Off) refers to control.o(.text.Turn_Off) for [Anonymous Symbol]
    control.o(.text.PWM_Limit) refers to fcmple.o(.text) for __aeabi_fcmple
    control.o(.text.PWM_Limit) refers to fcmpge.o(.text) for __aeabi_fcmpge
    control.o(.ARM.exidx.text.PWM_Limit) refers to control.o(.text.PWM_Limit) for [Anonymous Symbol]
    show.o(.text.oled_show) refers to memseta.o(.text) for __aeabi_memclr
    show.o(.text.oled_show) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    show.o(.text.oled_show) refers to oled.o(.text.OLED_ShowNumber) for OLED_ShowNumber
    show.o(.text.oled_show) refers to fcmpge.o(.text) for __aeabi_fcmpge
    show.o(.text.oled_show) refers to fcmplt.o(.text) for __aeabi_fcmplt
    show.o(.text.oled_show) refers to fmul.o(.text) for __aeabi_fmul
    show.o(.text.oled_show) refers to ffixi.o(.text) for __aeabi_f2iz
    show.o(.text.oled_show) refers to control.o(.text.myabs) for myabs
    show.o(.text.oled_show) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    show.o(.text.oled_show) refers to empty.o(.data.Car_Mode) for Car_Mode
    show.o(.text.oled_show) refers to show.o(.rodata.str1.1) for [Anonymous Symbol]
    show.o(.text.oled_show) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    show.o(.text.oled_show) refers to oled.o(.text.OLED_Refresh_Gram) for OLED_Refresh_Gram
    show.o(.text.oled_show) refers to control.o(.data.Run_Mode) for Run_Mode
    show.o(.text.oled_show) refers to ir_module.o(.bss.ir_dh1_state) for ir_dh1_state
    show.o(.text.oled_show) refers to ir_module.o(.bss.ir_dh2_state) for ir_dh2_state
    show.o(.text.oled_show) refers to ir_module.o(.bss.ir_dh3_state) for ir_dh3_state
    show.o(.text.oled_show) refers to ir_module.o(.bss.ir_dh4_state) for ir_dh4_state
    show.o(.text.oled_show) refers to empty.o(.data.target_laps) for target_laps
    show.o(.text.oled_show) refers to empty.o(.bss.current_laps) for current_laps
    show.o(.text.oled_show) refers to empty.o(.bss.corner_count) for corner_count
    show.o(.text.oled_show) refers to empty.o(.bss.Move_Z) for Move_Z
    show.o(.text.oled_show) refers to control.o(.bss.MotorA) for MotorA
    show.o(.text.oled_show) refers to control.o(.bss.MotorB) for MotorB
    show.o(.text.oled_show) refers to empty.o(.bss.Voltage) for Voltage
    show.o(.text.oled_show) refers to control.o(.data.Flag_Stop) for Flag_Stop
    show.o(.ARM.exidx.text.oled_show) refers to show.o(.text.oled_show) for [Anonymous Symbol]
    show.o(.text.APP_Show) refers to fadd.o(.text) for __aeabi_fadd
    show.o(.text.APP_Show) refers to fdiv.o(.text) for __aeabi_fdiv
    show.o(.text.APP_Show) refers to ffixi.o(.text) for __aeabi_f2iz
    show.o(.text.APP_Show) refers to f2d.o(.text) for __aeabi_f2d
    show.o(.text.APP_Show) refers to dmul.o(.text) for __aeabi_dmul
    show.o(.text.APP_Show) refers to dfixi.o(.text) for __aeabi_d2iz
    show.o(.text.APP_Show) refers to fmul.o(.text) for __aeabi_fmul
    show.o(.text.APP_Show) refers to printfa.o(i.__0printf) for printf
    show.o(.text.APP_Show) refers to empty.o(.bss.Voltage) for Voltage
    show.o(.text.APP_Show) refers to empty.o(.bss.Velocity_Right) for Velocity_Right
    show.o(.text.APP_Show) refers to empty.o(.bss.Velocity_Left) for Velocity_Left
    show.o(.text.APP_Show) refers to show.o(.bss.APP_Show.flag) for [Anonymous Symbol]
    show.o(.text.APP_Show) refers to empty.o(.bss.PID_Send) for PID_Send
    show.o(.text.APP_Show) refers to show.o(.rodata.str1.1) for [Anonymous Symbol]
    show.o(.text.APP_Show) refers to empty.o(.data.RC_Velocity) for RC_Velocity
    show.o(.text.APP_Show) refers to control.o(.data.Velocity_KP) for Velocity_KP
    show.o(.text.APP_Show) refers to control.o(.data.Velocity_KI) for Velocity_KI
    show.o(.text.APP_Show) refers to ir_module.o(.data.Turn90Angle) for Turn90Angle
    show.o(.text.APP_Show) refers to ir_module.o(.data.maxTurnAngle) for maxTurnAngle
    show.o(.text.APP_Show) refers to ir_module.o(.data.midTurnAngle) for midTurnAngle
    show.o(.text.APP_Show) refers to ir_module.o(.data.minTurnAngle) for minTurnAngle
    show.o(.ARM.exidx.text.APP_Show) refers to show.o(.text.APP_Show) for [Anonymous Symbol]
    show.o(.text.DataScope) refers to datascope_dp.o(.text.DataScope_Get_Channel_Data) for DataScope_Get_Channel_Data
    show.o(.text.DataScope) refers to datascope_dp.o(.text.DataScope_Data_Generate) for DataScope_Data_Generate
    show.o(.ARM.exidx.text.DataScope) refers to show.o(.text.DataScope) for [Anonymous Symbol]
    datascope_dp.o(.ARM.exidx.text.Float2Byte) refers to datascope_dp.o(.text.Float2Byte) for [Anonymous Symbol]
    datascope_dp.o(.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.text.Float2Byte) for Float2Byte
    datascope_dp.o(.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.bss.DataScope_OutPut_Buffer) for DataScope_OutPut_Buffer
    datascope_dp.o(.ARM.exidx.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.text.DataScope_Get_Channel_Data) for [Anonymous Symbol]
    datascope_dp.o(.text.DataScope_Data_Generate) refers to datascope_dp.o(.bss.DataScope_OutPut_Buffer) for DataScope_OutPut_Buffer
    datascope_dp.o(.ARM.exidx.text.DataScope_Data_Generate) refers to datascope_dp.o(.text.DataScope_Data_Generate) for [Anonymous Symbol]
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_disableChannel) for DL_DMA_disableChannel
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setSrcAddr) for DL_DMA_setSrcAddr
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setDestAddr) for DL_DMA_setDestAddr
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setTransferSize) for DL_DMA_setTransferSize
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_enableChannel) for DL_DMA_enableChannel
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.bss.gBTPacket) for gBTPacket
    uart_callback.o(.ARM.exidx.text.BT_DAMConfig) refers to uart_callback.o(.text.BT_DAMConfig) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_disableChannel) refers to uart_callback.o(.text.DL_DMA_disableChannel) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setSrcAddr) refers to uart_callback.o(.text.DL_DMA_setSrcAddr) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setDestAddr) refers to uart_callback.o(.text.DL_DMA_setDestAddr) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setTransferSize) refers to uart_callback.o(.text.DL_DMA_setTransferSize) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_enableChannel) refers to uart_callback.o(.text.DL_DMA_enableChannel) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.DL_DMA_getTransferSize) for DL_DMA_getTransferSize
    uart_callback.o(.text.BTBufferHandler) refers to board.o(.text.Systick_getTick) for Systick_getTick
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.bt_control) for bt_control
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.BT_DAMConfig) for BT_DAMConfig
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.lastSize) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.handleflag) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.handleSize) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.gBTPacket) for gBTPacket
    uart_callback.o(.ARM.exidx.text.BTBufferHandler) refers to uart_callback.o(.text.BTBufferHandler) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_getTransferSize) refers to uart_callback.o(.text.DL_DMA_getTransferSize) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to fadd.o(.text) for __aeabi_fadd
    uart_callback.o(.text.bt_control) refers to dflti.o(.text) for __aeabi_i2d
    uart_callback.o(.text.bt_control) refers to pow.o(i.pow) for pow
    uart_callback.o(.text.bt_control) refers to f2d.o(.text) for __aeabi_f2d
    uart_callback.o(.text.bt_control) refers to dmul.o(.text) for __aeabi_dmul
    uart_callback.o(.text.bt_control) refers to dadd.o(.text) for __aeabi_dadd
    uart_callback.o(.text.bt_control) refers to d2f.o(.text) for __aeabi_d2f
    uart_callback.o(.text.bt_control) refers to fdiv.o(.text) for __aeabi_fdiv
    uart_callback.o(.text.bt_control) refers to memseta.o(.text) for __aeabi_memclr
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.Usart_Receive) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.Turn_Flag) for Turn_Flag
    uart_callback.o(.text.bt_control) refers to empty.o(.data.RC_Velocity) for RC_Velocity
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.Flag_PID) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.i) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.Receive) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.j) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to uart_callback.o(.bss.bt_control.Data) for [Anonymous Symbol]
    uart_callback.o(.text.bt_control) refers to ir_module.o(.data.minTurnAngle) for minTurnAngle
    uart_callback.o(.text.bt_control) refers to ir_module.o(.data.midTurnAngle) for midTurnAngle
    uart_callback.o(.text.bt_control) refers to ir_module.o(.data.maxTurnAngle) for maxTurnAngle
    uart_callback.o(.text.bt_control) refers to ir_module.o(.data.Turn90Angle) for Turn90Angle
    uart_callback.o(.text.bt_control) refers to control.o(.data.Velocity_KI) for Velocity_KI
    uart_callback.o(.text.bt_control) refers to control.o(.data.Velocity_KP) for Velocity_KP
    uart_callback.o(.text.bt_control) refers to empty.o(.bss.PID_Send) for PID_Send
    uart_callback.o(.ARM.exidx.text.bt_control) refers to uart_callback.o(.text.bt_control) for [Anonymous Symbol]
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.BT_DAMConfig) for BT_DAMConfig
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.bss.gBTCounts) for gBTCounts
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.bss.gBTPacket) for gBTPacket
    uart_callback.o(.ARM.exidx.text.UART1_IRQHandler) refers to uart_callback.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to uart_callback.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_UART_receiveData) refers to uart_callback.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to board.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to board.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to board.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to board.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_mspm0g350x_uvision.o(HEAP), (4096 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_VOLTAGE_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (52 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (52 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableLoopbackMode), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing empty.o(.bss.lap_count), (4 bytes).
    Removing empty.o(.bss.Motor_Left), (4 bytes).
    Removing empty.o(.bss.Motor_Right), (4 bytes).
    Removing empty.o(.bss.RC_Turn_Velocity), (4 bytes).
    Removing empty.o(.bss.Move_Y), (4 bytes).
    Removing empty.o(.bss.PS2_ON_Flag), (4 bytes).
    Removing empty.o(.bss.test_num), (2 bytes).
    Removing empty.o(.bss.show_cnt), (2 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing board.o(.text), (0 bytes).
    Removing board.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing board.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing board.o(.text.fputs), (80 bytes).
    Removing board.o(.ARM.exidx.text.fputs), (8 bytes).
    Removing board.o(.text.puts), (48 bytes).
    Removing board.o(.ARM.exidx.text.puts), (8 bytes).
    Removing board.o(.ARM.exidx.text.Systick_getTick), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing board.o(.text.delay_1us), (16 bytes).
    Removing board.o(.ARM.exidx.text.delay_1us), (8 bytes).
    Removing board.o(.text.delay_1ms), (16 bytes).
    Removing board.o(.ARM.exidx.text.delay_1ms), (8 bytes).
    Removing board.o(.rodata.str1.1), (2 bytes).
    Removing board.o(.bss.tick_ms), (4 bytes).
    Removing board.o(.bss.start_time), (4 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (52 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (36 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (36 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_disable), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (36 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (142 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setOversampling), (30 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setBaudRateDivisor), (76 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (64 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (74 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (32 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_isRXFIFOEmpty), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_isRXFIFOEmpty), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveData), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_isTXFIFOFull), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (58 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (70 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (70 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (200 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (228 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_enable), (22 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (256 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (276 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (88 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (128 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_sendCommand), (36 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_sendCommand), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (52 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setLoadValue), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCounterValueAfterEnable), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (280 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getInChanConfig), (150 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCCPDirection), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (180 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (176 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getInChanPairConfig), (82 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanPairConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (184 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (156 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (56 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (54 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (42 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (444 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (480 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (52 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (676 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (696 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (64 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.text.DL_SPI_init), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_setClockConfig), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_isRXFIFOEmpty), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_isRXFIFOEmpty), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_isTXFIFOFull), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_isTXFIFOFull), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData8), (22 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData16), (22 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (60 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (60 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (56 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (70 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (70 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (172 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (200 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_enable), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_enable), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (216 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setClockFormat), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary), (32 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (176 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getClockFormat), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary), (20 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (134 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (140 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (134 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (140 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary), (20 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (76 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_getGain), (32 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_getGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_setGain), (40 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_setGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (80 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.HW_RD_FIELD32_RAW), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_RD_FIELD32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.HW_WR_FIELD32_RAW), (50 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_WR_FIELD32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (388 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.HW_RD_REG32_RAW), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_RD_REG32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.HW_WR_REG32_RAW), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_WR_REG32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessUnlock), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessLock), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (300 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (116 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccLoadRegister), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccLoadRegister), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (324 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (680 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (180 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getMsgObjSize), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getMsgObjSize), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsg), (320 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (304 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsg), (382 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (248 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (116 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (140 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (100 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (160 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (104 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (100 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (196 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (136 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (136 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (92 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (252 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (104 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (92 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (156 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (148 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (192 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (576 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (696 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getDataSize), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getDataSize), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (64 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_setOperandTwo), (24 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandTwo), (8 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_setOperandOne), (24 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandOne), (8 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (84 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOFull), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitControllerData), (22 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitControllerData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerRXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (84 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOFull), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetData), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetRXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getTargetStatus), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getTargetStatus), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (60 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (34 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetData), (18 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (58 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setCommandAddress), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (64 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (84 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_waitForCmdDone), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getDATAFlashSize), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (378 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks), (60 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getNumBanks), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getMAINFlashSize), (8 bytes).
    Removing dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled), (4 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isFlashBankSwapEnabled), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_enableAddressOverrideMode), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setBankSelect), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setBankSelect), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setRegionSelect), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_disableAddressOverrideMode), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (60 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (148 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (348 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (146 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (158 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (156 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (176 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (178 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber), (14 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumber), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank), (82 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumberInBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank), (4 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isExecuteFromUpperFlashBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (356 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (68 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (84 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (104 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_CORE_getInstructionConfig), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_CORE_getInstructionConfig), (8 bytes).
    Removing dl_flashctl.o(.text.DL_CORE_configInstruction), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_CORE_configInstruction), (8 bytes).
    Removing dl_flashctl.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryConfig), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyConfig), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_configTransfer), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_setTrigger), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (120 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_configDataFormat), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_configDataFormat), (8 bytes).
    Removing dl_dac12.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_isFIFOFull), (34 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_isFIFOFull), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_output8), (22 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_output8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_output12), (28 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_output12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (70 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (72 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (32 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_startCalibration), (18 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_startCalibration), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_isCalibrationRunning), (30 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_isCalibrationRunning), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_getInterruptStatus), (24 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_getInterruptStatus), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (70 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_setSeed32), (24 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_feedData32), (24 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_feedData32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_getResult32), (20 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_getResult32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (64 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (82 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_setSeed16), (36 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_feedData16), (36 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_feedData16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_getResult16), (20 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_getResult16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (72 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (64 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_checkAlignmentAndReturnPointer), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (80 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataWord), (66 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataWord), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (58 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (98 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (86 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (132 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (132 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (76 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_interrupt.o(.text), (0 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_registerInterrupt), (92 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt), (8 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt), (28 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt), (8 bytes).
    Removing dl_interrupt.o(.vtable), (192 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Refresh_Gram), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WR_Byte), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RS_Set), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RS_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SCLK_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SDIN_Set), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SDIN_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SCLK_Set), (8 bytes).
    Removing oled.o(.text.OLED_Display_On), (34 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_On), (8 bytes).
    Removing oled.o(.text.OLED_Display_Off), (34 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_Off), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawPoint), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.ARM.exidx.text.oled_pow), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNumber), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RST_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RST_Set), (8 bytes).
    Removing oled.o(.text.OLED_ShowCHinese), (160 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowCHinese), (8 bytes).
    Removing oled.o(.text.OLED_Set_Pos), (60 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing oled.o(.rodata.Hzk16), (2624 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.ARM.exidx.text.keyValue), (8 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing key.o(.ARM.exidx.text.key_scan), (8 bytes).
    Removing key.o(.ARM.exidx.text.lapsKeyValue), (8 bytes).
    Removing key.o(.ARM.exidx.text.laps_key_scan), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.ARM.exidx.text.LED_ON), (8 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing led.o(.text.LED_OFF), (20 bytes).
    Removing led.o(.ARM.exidx.text.LED_OFF), (8 bytes).
    Removing led.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing led.o(.ARM.exidx.text.LED_Toggle), (8 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_togglePins), (8 bytes).
    Removing led.o(.ARM.exidx.text.LED_Flash), (8 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.ARM.exidx.text.Set_PWM), (8 bytes).
    Removing motor.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing motor.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing encoder.o(.text), (0 bytes).
    Removing encoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing adc.o(.text), (0 bytes).
    Removing adc.o(.ARM.exidx.text.Get_battery_volt), (8 bytes).
    Removing adc.o(.ARM.exidx.text.DL_ADC12_startConversion), (8 bytes).
    Removing adc.o(.ARM.exidx.text.DL_ADC12_getMemResult), (8 bytes).
    Removing adc.o(.ARM.exidx.text.ADC1_IRQHandler), (8 bytes).
    Removing adc.o(.ARM.exidx.text.DL_ADC12_getPendingInterrupt), (8 bytes).
    Removing ir_module.o(.text), (0 bytes).
    Removing ir_module.o(.ARM.exidx.text.IRDM_line_inspection), (8 bytes).
    Removing ir_module.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing lcd.o(.text), (0 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Fill), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawPoint), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawLine), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawRectangle), (8 bytes).
    Removing lcd.o(.text.Draw_Circle), (276 bytes).
    Removing lcd.o(.ARM.exidx.text.Draw_Circle), (8 bytes).
    Removing lcd.o(.text.LCD_ShowChinese), (278 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowChinese), (8 bytes).
    Removing lcd.o(.text.LCD_ShowChinese12x12), (472 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12), (8 bytes).
    Removing lcd.o(.text.LCD_ShowChinese16x16), (472 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16), (8 bytes).
    Removing lcd.o(.text.LCD_ShowChinese24x24), (472 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24), (8 bytes).
    Removing lcd.o(.text.LCD_ShowChinese32x32), (472 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowChar), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowString), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.mypow), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowIntNum), (8 bytes).
    Removing lcd.o(.text.LCD_ShowFloatNum1), (284 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1), (8 bytes).
    Removing lcd.o(.text.LCD_ShowPicture), (160 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ShowPicture), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Display_Status), (8 bytes).
    Removing lcd.o(.rodata.tfont12), (130 bytes).
    Removing lcd.o(.rodata.tfont16), (170 bytes).
    Removing lcd.o(.rodata.tfont24), (370 bytes).
    Removing lcd.o(.rodata.tfont32), (650 bytes).
    Removing lcd_init.o(.text), (0 bytes).
    Removing lcd_init.o(.ARM.exidx.text.LCD_GPIO_Init), (8 bytes).
    Removing lcd_init.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing lcd_init.o(.ARM.exidx.text.LCD_Writ_Bus), (8 bytes).
    Removing lcd_init.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing lcd_init.o(.ARM.exidx.text.LCD_WR_DATA8), (8 bytes).
    Removing lcd_init.o(.ARM.exidx.text.LCD_WR_DATA), (8 bytes).
    Removing lcd_init.o(.ARM.exidx.text.LCD_WR_REG), (8 bytes).
    Removing lcd_init.o(.ARM.exidx.text.LCD_Address_Set), (8 bytes).
    Removing lcd_init.o(.ARM.exidx.text.LCD_Init), (8 bytes).
    Removing control.o(.text), (0 bytes).
    Removing control.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing control.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing control.o(.ARM.exidx.text.Key), (8 bytes).
    Removing control.o(.ARM.exidx.text.Get_Velocity_From_Encoder), (8 bytes).
    Removing control.o(.ARM.exidx.text.Get_Target_Encoder), (8 bytes).
    Removing control.o(.ARM.exidx.text.Incremental_PI_Left), (8 bytes).
    Removing control.o(.ARM.exidx.text.Incremental_PI_Right), (8 bytes).
    Removing control.o(.ARM.exidx.text.myabs), (8 bytes).
    Removing control.o(.text.Turn_Off), (14 bytes).
    Removing control.o(.ARM.exidx.text.Turn_Off), (8 bytes).
    Removing control.o(.text.PWM_Limit), (60 bytes).
    Removing control.o(.ARM.exidx.text.PWM_Limit), (8 bytes).
    Removing control.o(.bss.CCD_count), (1 bytes).
    Removing control.o(.bss.ELE_count), (1 bytes).
    Removing control.o(.bss.Sensor_Left), (4 bytes).
    Removing control.o(.bss.Sensor_Middle), (4 bytes).
    Removing control.o(.bss.Sensor_Right), (4 bytes).
    Removing control.o(.bss.Sensor), (4 bytes).
    Removing show.o(.text), (0 bytes).
    Removing show.o(.ARM.exidx.text.oled_show), (8 bytes).
    Removing show.o(.ARM.exidx.text.APP_Show), (8 bytes).
    Removing show.o(.text.DataScope), (82 bytes).
    Removing show.o(.ARM.exidx.text.DataScope), (8 bytes).
    Removing datascope_dp.o(.text), (0 bytes).
    Removing datascope_dp.o(.text.Float2Byte), (64 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.Float2Byte), (8 bytes).
    Removing datascope_dp.o(.text.DataScope_Get_Channel_Data), (224 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.DataScope_Get_Channel_Data), (8 bytes).
    Removing datascope_dp.o(.text.DataScope_Data_Generate), (268 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.DataScope_Data_Generate), (8 bytes).
    Removing datascope_dp.o(.bss.DataScope_OutPut_Buffer), (42 bytes).
    Removing uart_callback.o(.text), (0 bytes).
    Removing uart_callback.o(.ARM.exidx.text.BT_DAMConfig), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_disableChannel), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setSrcAddr), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setDestAddr), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setTransferSize), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_enableChannel), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.BTBufferHandler), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_getTransferSize), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.bt_control), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing uart_callback.o(.bss.Flag_Direction), (4 bytes).
    Removing uart_callback.o(.bss.lastBTCounts), (1 bytes).
    Removing uart_callback.o(.bss.RecvOverFlag), (1 bytes).
    Removing uart_callback.o(.bss.Flag_Left), (4 bytes).
    Removing uart_callback.o(.bss.Flag_Right), (4 bytes).
    Removing uart_callback.o(.bss.gCheckBT), (1 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (84 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

1243 unused section(s) (total 50829 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmplt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpge.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    DataScope_DP.C                           0x00000000   Number         0  datascope_dp.o ABSOLUTE
    IR_Module.c                              0x00000000   Number         0  ir_module.o ABSOLUTE
    adc.c                                    0x00000000   Number         0  adc.o ABSOLUTE
    board.c                                  0x00000000   Number         0  board.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    control.c                                0x00000000   Number         0  control.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_aes.c                                 0x00000000   Number         0  dl_aes.o ABSOLUTE
    dl_aesadv.c                              0x00000000   Number         0  dl_aesadv.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_crc.c                                 0x00000000   Number         0  dl_crc.o ABSOLUTE
    dl_crcp.c                                0x00000000   Number         0  dl_crcp.o ABSOLUTE
    dl_dac12.c                               0x00000000   Number         0  dl_dac12.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_flashctl.c                            0x00000000   Number         0  dl_flashctl.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_interrupt.c                           0x00000000   Number         0  dl_interrupt.o ABSOLUTE
    dl_keystorectl.c                         0x00000000   Number         0  dl_keystorectl.o ABSOLUTE
    dl_lcd.c                                 0x00000000   Number         0  dl_lcd.o ABSOLUTE
    dl_lfss.c                                0x00000000   Number         0  dl_lfss.o ABSOLUTE
    dl_mathacl.c                             0x00000000   Number         0  dl_mathacl.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_opa.c                                 0x00000000   Number         0  dl_opa.o ABSOLUTE
    dl_rtc_common.c                          0x00000000   Number         0  dl_rtc_common.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_trng.c                                0x00000000   Number         0  dl_trng.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    dl_vref.c                                0x00000000   Number         0  dl_vref.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    encoder.c                                0x00000000   Number         0  encoder.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    key.c                                    0x00000000   Number         0  key.o ABSOLUTE
    lcd.c                                    0x00000000   Number         0  lcd.o ABSOLUTE
    lcd_init.c                               0x00000000   Number         0  lcd_init.o ABSOLUTE
    led.c                                    0x00000000   Number         0  led.o ABSOLUTE
    motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    show.c                                   0x00000000   Number         0  show.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    uart_callback.c                          0x00000000   Number         0  uart_callback.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  memseta.o(.text)
    .text                                    0x0000010c   Section        0  fadd.o(.text)
    .text                                    0x000001be   Section        0  fmul.o(.text)
    .text                                    0x00000238   Section        0  fdiv.o(.text)
    .text                                    0x000002b4   Section        0  dadd.o(.text)
    .text                                    0x00000418   Section        0  dmul.o(.text)
    .text                                    0x000004e8   Section        0  ddiv.o(.text)
    .text                                    0x000005d8   Section        0  fcmple.o(.text)
    .text                                    0x000005f4   Section        0  fcmplt.o(.text)
    .text                                    0x00000610   Section        0  fcmpge.o(.text)
    .text                                    0x0000062c   Section        0  fflti.o(.text)
    .text                                    0x00000642   Section        0  ffltui.o(.text)
    .text                                    0x00000650   Section        0  dflti.o(.text)
    .text                                    0x00000678   Section        0  ffixi.o(.text)
    .text                                    0x000006ac   Section        0  dfixi.o(.text)
    .text                                    0x000006f4   Section        0  f2d.o(.text)
    .text                                    0x0000071c   Section        0  d2f.o(.text)
    .text                                    0x00000754   Section        0  uidiv_div0.o(.text)
    .text                                    0x00000792   Section        0  uldiv.o(.text)
    .text                                    0x000007f2   Section        0  llshl.o(.text)
    .text                                    0x00000812   Section        0  llushr.o(.text)
    .text                                    0x00000834   Section        0  llsshr.o(.text)
    .text                                    0x0000085a   Section        0  iusefp.o(.text)
    .text                                    0x0000085a   Section        0  fepilogue.o(.text)
    .text                                    0x000008dc   Section        0  depilogue.o(.text)
    .text                                    0x0000099a   Section        0  dscalb.o(.text)
    .text                                    0x000009c8   Section        0  dfixul.o(.text)
    .text                                    0x00000a08   Section       40  cdrcmple.o(.text)
    .text                                    0x00000a30   Section       48  init.o(.text)
    .text                                    0x00000a60   Section        0  dsqrt.o(.text)
    [Anonymous Symbol]                       0x00000b04   Section        0  adc.o(.text.ADC1_IRQHandler)
    __arm_cp.3_0                             0x00000b20   Number         4  adc.o(.text.ADC1_IRQHandler)
    __arm_cp.3_1                             0x00000b24   Number         4  adc.o(.text.ADC1_IRQHandler)
    [Anonymous Symbol]                       0x00000b28   Section        0  show.o(.text.APP_Show)
    __arm_cp.1_0                             0x00000c7c   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_1                             0x00000c80   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_2                             0x00000c84   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_3                             0x00000c88   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_4                             0x00000c8c   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_5                             0x00000c90   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_6                             0x00000c94   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_7                             0x00000c98   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_8                             0x00000c9c   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_9                             0x00000ca0   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_10                            0x00000ca4   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_11                            0x00000ca8   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_12                            0x00000cac   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_13                            0x00000cb0   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_14                            0x00000cb4   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_15                            0x00000cb8   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_16                            0x00000cbc   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_17                            0x00000cc0   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_18                            0x00000cc4   Number         4  show.o(.text.APP_Show)
    __arm_cp.1_19                            0x00000cc8   Number         4  show.o(.text.APP_Show)
    [Anonymous Symbol]                       0x00000ccc   Section        0  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_1                             0x00000d88   Number         4  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_2                             0x00000d8c   Number         4  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_3                             0x00000d90   Number         4  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_4                             0x00000d94   Number         4  uart_callback.o(.text.BTBufferHandler)
    [Anonymous Symbol]                       0x00000d98   Section        0  uart_callback.o(.text.BT_DAMConfig)
    __arm_cp.0_0                             0x00000dd4   Number         4  uart_callback.o(.text.BT_DAMConfig)
    __arm_cp.0_1                             0x00000dd8   Number         4  uart_callback.o(.text.BT_DAMConfig)
    __arm_cp.0_2                             0x00000ddc   Number         4  uart_callback.o(.text.BT_DAMConfig)
    DL_ADC12_clearInterruptStatus            0x00000de1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000de0   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus)
    __arm_cp.52_0                            0x00000df8   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus)
    DL_ADC12_configConversionMem             0x00000dfd   Thumb Code    74  ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem)
    [Anonymous Symbol]                       0x00000dfc   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem)
    DL_ADC12_enableConversions               0x00000e47   Thumb Code    22  ti_msp_dl_config.o(.text.DL_ADC12_enableConversions)
    [Anonymous Symbol]                       0x00000e46   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enableConversions)
    DL_ADC12_enableInterrupt                 0x00000e5d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt)
    [Anonymous Symbol]                       0x00000e5c   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt)
    __arm_cp.53_0                            0x00000e74   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt)
    DL_ADC12_enablePower                     0x00000e79   Thumb Code    20  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    [Anonymous Symbol]                       0x00000e78   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    __arm_cp.20_0                            0x00000e8c   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    DL_ADC12_getMemResult                    0x00000e91   Thumb Code    44  adc.o(.text.DL_ADC12_getMemResult)
    [Anonymous Symbol]                       0x00000e90   Section        0  adc.o(.text.DL_ADC12_getMemResult)
    __arm_cp.2_0                             0x00000ebc   Number         4  adc.o(.text.DL_ADC12_getMemResult)
    DL_ADC12_getPendingInterrupt             0x00000ec1   Thumb Code    18  adc.o(.text.DL_ADC12_getPendingInterrupt)
    [Anonymous Symbol]                       0x00000ec0   Section        0  adc.o(.text.DL_ADC12_getPendingInterrupt)
    DL_ADC12_initSingleSample                0x00000ed5   Thumb Code    60  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    [Anonymous Symbol]                       0x00000ed4   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.49_1                            0x00000f10   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.49_2                            0x00000f14   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.49_3                            0x00000f18   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    DL_ADC12_reset                           0x00000f1d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    [Anonymous Symbol]                       0x00000f1c   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    __arm_cp.16_0                            0x00000f2c   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    __arm_cp.16_1                            0x00000f30   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    [Anonymous Symbol]                       0x00000f34   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x00000f74   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x00000f78   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_ADC12_setSampleTime0                  0x00000f7d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    [Anonymous Symbol]                       0x00000f7c   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    __arm_cp.51_0                            0x00000f90   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    DL_ADC12_startConversion                 0x00000f95   Thumb Code    24  adc.o(.text.DL_ADC12_startConversion)
    [Anonymous Symbol]                       0x00000f94   Section        0  adc.o(.text.DL_ADC12_startConversion)
    __arm_cp.1_0                             0x00000fac   Number         4  adc.o(.text.DL_ADC12_startConversion)
    [Anonymous Symbol]                       0x00000fb0   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x00000fc5   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000fc4   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x00000fed   Thumb Code    40  dl_uart.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000fec   Section        0  dl_uart.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x00001015   Thumb Code    40  dl_timer.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00001014   Section        0  dl_timer.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x0000103d   Thumb Code    40  dl_dma.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x0000103c   Section        0  dl_dma.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x00001065   Thumb Code    40  dl_adc12.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00001064   Section        0  dl_adc12.o(.text.DL_Common_updateReg)
    DL_DMA_configTransfer                    0x0000108d   Thumb Code    84  dl_dma.o(.text.DL_DMA_configTransfer)
    [Anonymous Symbol]                       0x0000108c   Section        0  dl_dma.o(.text.DL_DMA_configTransfer)
    DL_DMA_disableChannel                    0x000010e1   Thumb Code    38  uart_callback.o(.text.DL_DMA_disableChannel)
    [Anonymous Symbol]                       0x000010e0   Section        0  uart_callback.o(.text.DL_DMA_disableChannel)
    DL_DMA_enableChannel                     0x00001107   Thumb Code    38  uart_callback.o(.text.DL_DMA_enableChannel)
    [Anonymous Symbol]                       0x00001106   Section        0  uart_callback.o(.text.DL_DMA_enableChannel)
    DL_DMA_getTransferSize                   0x0000112d   Thumb Code    32  uart_callback.o(.text.DL_DMA_getTransferSize)
    [Anonymous Symbol]                       0x0000112c   Section        0  uart_callback.o(.text.DL_DMA_getTransferSize)
    [Anonymous Symbol]                       0x0000114c   Section        0  dl_dma.o(.text.DL_DMA_initChannel)
    DL_DMA_setDestAddr                       0x00001195   Thumb Code    36  uart_callback.o(.text.DL_DMA_setDestAddr)
    [Anonymous Symbol]                       0x00001194   Section        0  uart_callback.o(.text.DL_DMA_setDestAddr)
    __arm_cp.3_0                             0x000011b8   Number         4  uart_callback.o(.text.DL_DMA_setDestAddr)
    DL_DMA_setSrcAddr                        0x000011bd   Thumb Code    36  uart_callback.o(.text.DL_DMA_setSrcAddr)
    [Anonymous Symbol]                       0x000011bc   Section        0  uart_callback.o(.text.DL_DMA_setSrcAddr)
    __arm_cp.2_0                             0x000011e0   Number         4  uart_callback.o(.text.DL_DMA_setSrcAddr)
    DL_DMA_setTransferSize                   0x000011e5   Thumb Code    44  uart_callback.o(.text.DL_DMA_setTransferSize)
    [Anonymous Symbol]                       0x000011e4   Section        0  uart_callback.o(.text.DL_DMA_setTransferSize)
    __arm_cp.4_0                             0x00001210   Number         4  uart_callback.o(.text.DL_DMA_setTransferSize)
    DL_DMA_setTrigger                        0x00001215   Thumb Code    52  dl_dma.o(.text.DL_DMA_setTrigger)
    [Anonymous Symbol]                       0x00001214   Section        0  dl_dma.o(.text.DL_DMA_setTrigger)
    __arm_cp.2_0                             0x00001248   Number         4  dl_dma.o(.text.DL_DMA_setTrigger)
    DL_GPIO_clearInterruptStatus             0x0000124d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x0000124c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x00001265   Thumb Code    24  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00001264   Section        0  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.3_0                             0x0000127c   Number         4  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x00001281   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001280   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00001295   Thumb Code    20  oled.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001294   Section        0  oled.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000012a9   Thumb Code    20  led.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000012a8   Section        0  led.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000012bd   Thumb Code    20  motor.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000012bc   Section        0  motor.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000012d1   Thumb Code    20  lcd_init.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000012d0   Section        0  lcd_init.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableInterrupt                  0x000012e5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x000012e4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.29_0                            0x000012fc   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x00001301   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00001300   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.22_0                            0x00001314   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00001319   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00001318   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    __arm_cp.17_0                            0x0000132c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x00001331   Thumb Code    20  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x00001330   Section        0  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.1_0                             0x00001344   Number         4  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInput                 0x00001349   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput)
    [Anonymous Symbol]                       0x00001348   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput)
    __arm_cp.25_1                            0x0000135c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput)
    DL_GPIO_initDigitalOutput                0x00001361   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x00001360   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initPeripheralInputFunction      0x00001375   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00001374   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    __arm_cp.23_0                            0x0000138c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralOutputFunction     0x00001391   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x00001390   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.21_0                            0x000013a8   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x000013ad   Thumb Code    22  key.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x000013ac   Section        0  key.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x000013c3   Thumb Code    22  encoder.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x000013c2   Section        0  encoder.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x000013d9   Thumb Code    22  ir_module.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x000013d8   Section        0  ir_module.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x000013f1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x000013f0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.13_0                            0x00001400   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.13_1                            0x00001404   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setPins                          0x00001409   Thumb Code    20  oled.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00001408   Section        0  oled.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x0000141d   Thumb Code    20  motor.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000141c   Section        0  motor.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00001431   Thumb Code    20  lcd_init.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00001430   Section        0  lcd_init.o(.text.DL_GPIO_setPins)
    __arm_cp.1_0                             0x00001444   Number         4  lcd_init.o(.text.DL_GPIO_setPins)
    DL_GPIO_setUpperPinsPolarity             0x00001449   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    [Anonymous Symbol]                       0x00001448   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    DL_GPIO_togglePins                       0x00001461   Thumb Code    20  led.o(.text.DL_GPIO_togglePins)
    [Anonymous Symbol]                       0x00001460   Section        0  led.o(.text.DL_GPIO_togglePins)
    __arm_cp.5_0                             0x00001474   Number         4  led.o(.text.DL_GPIO_togglePins)
    [Anonymous Symbol]                       0x00001478   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x0000152c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00001530   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00001534   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x00001539   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x00001538   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00001545   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00001544   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.34_0                            0x00001554   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_setBORThreshold                0x00001559   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x00001558   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.30_0                            0x0000156c   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setFlashWaitState              0x00001571   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00001570   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    DL_SYSCTL_setSYSOSCFreq                  0x0000158d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x0000158c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.32_0                            0x000015a4   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSCTL_setULPCLKDivider               0x000015a9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x000015a8   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x000015c0   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x000015e0   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x000015e4   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_SYSTICK_enable                        0x000015e9   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    [Anonymous Symbol]                       0x000015e8   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    DL_SYSTICK_init                          0x000015f5   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x000015f4   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.56_0                            0x00001610   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.56_1                            0x00001614   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.56_2                            0x00001618   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x0000161c   Section        0  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_enableClock                     0x00001709   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00001708   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    __arm_cp.37_0                            0x00001718   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enableInterrupt                 0x0000171d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    [Anonymous Symbol]                       0x0000171c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    __arm_cp.39_0                            0x00001734   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    DL_Timer_enablePower                     0x00001739   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00001738   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    __arm_cp.18_0                            0x0000174c   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    DL_Timer_getPendingInterrupt             0x00001751   Thumb Code    18  control.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001750   Section        0  control.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001764   Section        0  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.17_0                            0x00001868   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.17_2                            0x0000186c   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    [Anonymous Symbol]                       0x00001870   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_reset                           0x0000196d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x0000196c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x0000197d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x0000197c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00001990   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x000019c0   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareAction)
    __arm_cp.18_0                            0x000019ec   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareAction)
    __arm_cp.18_1                            0x000019f0   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareAction)
    [Anonymous Symbol]                       0x000019f4   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    __arm_cp.6_0                             0x00001a28   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    __arm_cp.6_1                             0x00001a2c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    [Anonymous Symbol]                       0x00001a30   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareInput)
    [Anonymous Symbol]                       0x00001a64   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.19_0                            0x00001aa0   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00001aa4   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.5_0                             0x00001acc   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00001ad0   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterValueAfterEnable      0x00001afd   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable)
    [Anonymous Symbol]                       0x00001afc   Section        0  dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable)
    __arm_cp.4_0                             0x00001b18   Number         4  dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable)
    DL_Timer_setLoadValue                    0x00001b1d   Thumb Code    20  dl_timer.o(.text.DL_Timer_setLoadValue)
    [Anonymous Symbol]                       0x00001b1c   Section        0  dl_timer.o(.text.DL_Timer_setLoadValue)
    __arm_cp.3_0                             0x00001b30   Number         4  dl_timer.o(.text.DL_Timer_setLoadValue)
    DL_UART_disable                          0x00001b35   Thumb Code    22  dl_uart.o(.text.DL_UART_disable)
    [Anonymous Symbol]                       0x00001b34   Section        0  dl_uart.o(.text.DL_UART_disable)
    DL_UART_enable                           0x00001b4b   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x00001b4a   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableDMAReceiveEvent            0x00001b61   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    [Anonymous Symbol]                       0x00001b60   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    __arm_cp.48_0                            0x00001b74   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    DL_UART_enableFIFOs                      0x00001b79   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    [Anonymous Symbol]                       0x00001b78   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    DL_UART_enableInterrupt                  0x00001b91   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x00001b90   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.47_0                            0x00001ba8   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enableLoopbackMode               0x00001bad   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode)
    [Anonymous Symbol]                       0x00001bac   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode)
    DL_UART_enablePower                      0x00001bc5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x00001bc4   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.19_0                            0x00001bd8   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x00001bdd   Thumb Code    18  uart_callback.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001bdc   Section        0  uart_callback.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001bf0   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00001c34   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_isBusy                           0x00001c39   Thumb Code    20  board.o(.text.DL_UART_isBusy)
    [Anonymous Symbol]                       0x00001c38   Section        0  board.o(.text.DL_UART_isBusy)
    DL_UART_isTXFIFOFull                     0x00001c4d   Thumb Code    20  dl_uart.o(.text.DL_UART_isTXFIFOFull)
    [Anonymous Symbol]                       0x00001c4c   Section        0  dl_uart.o(.text.DL_UART_isTXFIFOFull)
    __arm_cp.14_0                            0x00001c60   Number         4  dl_uart.o(.text.DL_UART_isTXFIFOFull)
    DL_UART_receiveData                      0x00001c65   Thumb Code    16  uart_callback.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x00001c64   Section        0  uart_callback.o(.text.DL_UART_receiveData)
    __arm_cp.11_0                            0x00001c74   Number         4  uart_callback.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x00001c79   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x00001c78   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.15_0                            0x00001c88   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.15_1                            0x00001c8c   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x00001c91   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001c90   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.41_0                            0x00001ccc   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.41_1                            0x00001cd0   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.41_2                            0x00001cd4   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.41_3                            0x00001cd8   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001cdc   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    __arm_cp.3_0                             0x00001cfc   Number         4  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x00001d01   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x00001d00   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_setRXFIFOThreshold               0x00001d21   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    [Anonymous Symbol]                       0x00001d20   Section        0  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    DL_UART_setTXFIFOThreshold               0x00001d45   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    [Anonymous Symbol]                       0x00001d44   Section        0  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    __arm_cp.44_0                            0x00001d68   Number         4  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    DL_UART_transmitData                     0x00001d6d   Thumb Code    22  dl_uart.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x00001d6c   Section        0  dl_uart.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x00001d82   Section        0  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    [Anonymous Symbol]                       0x00001dac   Section        0  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_0                             0x00001eb4   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_1                             0x00001eb8   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_2                             0x00001ebc   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_3                             0x00001ec0   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_4                             0x00001ec4   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_5                             0x00001ec8   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00001ecc   Section        0  control.o(.text.Get_Target_Encoder)
    __arm_cp.4_0                             0x00001f3c   Number         4  control.o(.text.Get_Target_Encoder)
    __arm_cp.4_1                             0x00001f40   Number         4  control.o(.text.Get_Target_Encoder)
    [Anonymous Symbol]                       0x00001f44   Section        0  control.o(.text.Get_Velocity_From_Encoder)
    __arm_cp.3_0                             0x00001fc4   Number         4  control.o(.text.Get_Velocity_From_Encoder)
    __arm_cp.3_1                             0x00001fc8   Number         4  control.o(.text.Get_Velocity_From_Encoder)
    __arm_cp.3_2                             0x00001fcc   Number         4  control.o(.text.Get_Velocity_From_Encoder)
    __arm_cp.3_3                             0x00001fd0   Number         4  control.o(.text.Get_Velocity_From_Encoder)
    __arm_cp.3_4                             0x00001fd4   Number         4  control.o(.text.Get_Velocity_From_Encoder)
    __arm_cp.3_5                             0x00001fd8   Number         4  control.o(.text.Get_Velocity_From_Encoder)
    __arm_cp.3_6                             0x00001fdc   Number         4  control.o(.text.Get_Velocity_From_Encoder)
    [Anonymous Symbol]                       0x00001fe0   Section        0  adc.o(.text.Get_battery_volt)
    __arm_cp.0_0                             0x0000202c   Number         4  adc.o(.text.Get_battery_volt)
    __arm_cp.0_1                             0x00002030   Number         4  adc.o(.text.Get_battery_volt)
    __arm_cp.0_2                             0x00002034   Number         4  adc.o(.text.Get_battery_volt)
    __arm_cp.0_3                             0x00002038   Number         4  adc.o(.text.Get_battery_volt)
    __arm_cp.0_4                             0x0000203c   Number         4  adc.o(.text.Get_battery_volt)
    __arm_cp.0_5                             0x00002040   Number         4  adc.o(.text.Get_battery_volt)
    [Anonymous Symbol]                       0x00002044   Section        0  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_32                            0x00002210   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_33                            0x00002214   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_34                            0x00002218   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_35                            0x0000221c   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_36                            0x00002220   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_37                            0x00002224   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_6                             0x00002488   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_7                             0x0000248c   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_8                             0x00002490   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_9                             0x00002494   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_10                            0x00002498   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_11                            0x0000249c   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_12                            0x000024a0   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_13                            0x000024a4   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_14                            0x000024a8   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_15                            0x000024ac   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_16                            0x000024b0   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_17                            0x000024b4   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_18                            0x000024b8   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_19                            0x000024bc   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_20                            0x000024c0   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_21                            0x000024c4   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_22                            0x000024c8   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_23                            0x000024cc   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_24                            0x000024d0   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_25                            0x000024d4   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_26                            0x000024d8   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_27                            0x000024dc   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_28                            0x000024e0   Number         4  ir_module.o(.text.IRDM_line_inspection)
    __arm_cp.0_30                            0x000024e4   Number         4  ir_module.o(.text.IRDM_line_inspection)
    [Anonymous Symbol]                       0x000024e8   Section        0  control.o(.text.Incremental_PI_Left)
    __arm_cp.5_0                             0x00002584   Number         4  control.o(.text.Incremental_PI_Left)
    __arm_cp.5_2                             0x00002588   Number         4  control.o(.text.Incremental_PI_Left)
    __arm_cp.5_4                             0x0000258c   Number         4  control.o(.text.Incremental_PI_Left)
    [Anonymous Symbol]                       0x00002590   Section        0  control.o(.text.Incremental_PI_Right)
    __arm_cp.6_0                             0x0000262c   Number         4  control.o(.text.Incremental_PI_Right)
    __arm_cp.6_1                             0x00002630   Number         4  control.o(.text.Incremental_PI_Right)
    __arm_cp.6_2                             0x00002634   Number         4  control.o(.text.Incremental_PI_Right)
    __arm_cp.6_3                             0x00002638   Number         4  control.o(.text.Incremental_PI_Right)
    __arm_cp.6_4                             0x0000263c   Number         4  control.o(.text.Incremental_PI_Right)
    __arm_cp.6_5                             0x00002640   Number         4  control.o(.text.Incremental_PI_Right)
    __arm_cp.6_6                             0x00002644   Number         4  control.o(.text.Incremental_PI_Right)
    [Anonymous Symbol]                       0x00002648   Section        0  control.o(.text.Key)
    __arm_cp.2_0                             0x000026c0   Number         4  control.o(.text.Key)
    __arm_cp.2_1                             0x000026c4   Number         4  control.o(.text.Key)
    __arm_cp.2_2                             0x000026c8   Number         4  control.o(.text.Key)
    [Anonymous Symbol]                       0x000026cc   Section        0  lcd_init.o(.text.LCD_Address_Set)
    [Anonymous Symbol]                       0x00002720   Section        0  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_0                            0x00002928   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_1                            0x0000292c   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_2                            0x00002930   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_3                            0x00002934   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_4                            0x00002938   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_5                            0x0000293c   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_6                            0x00002940   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_7                            0x00002944   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_8                            0x00002948   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_9                            0x0000294c   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_10                           0x00002950   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_11                           0x00002954   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_12                           0x00002958   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_13                           0x0000295c   Number         4  lcd.o(.text.LCD_Display_Status)
    __arm_cp.16_14                           0x00002960   Number         4  lcd.o(.text.LCD_Display_Status)
    [Anonymous Symbol]                       0x00002964   Section        0  lcd.o(.text.LCD_DrawLine)
    [Anonymous Symbol]                       0x00002a8c   Section        0  lcd.o(.text.LCD_DrawPoint)
    [Anonymous Symbol]                       0x00002ab8   Section        0  lcd.o(.text.LCD_DrawRectangle)
    [Anonymous Symbol]                       0x00002b42   Section        0  lcd.o(.text.LCD_Fill)
    [Anonymous Symbol]                       0x00002bc8   Section        0  lcd_init.o(.text.LCD_GPIO_Init)
    [Anonymous Symbol]                       0x00002bf4   Section        0  lcd_init.o(.text.LCD_Init)
    __arm_cp.8_0                             0x00002db0   Number         4  lcd_init.o(.text.LCD_Init)
    [Anonymous Symbol]                       0x00002db4   Section        0  lcd.o(.text.LCD_ShowChar)
    __arm_cp.10_0                            0x00002fb8   Number         4  lcd.o(.text.LCD_ShowChar)
    __arm_cp.10_1                            0x00002fbc   Number         4  lcd.o(.text.LCD_ShowChar)
    __arm_cp.10_2                            0x00002fc0   Number         4  lcd.o(.text.LCD_ShowChar)
    __arm_cp.10_3                            0x00002fc4   Number         4  lcd.o(.text.LCD_ShowChar)
    [Anonymous Symbol]                       0x00002fc8   Section        0  lcd.o(.text.LCD_ShowIntNum)
    [Anonymous Symbol]                       0x000030e0   Section        0  lcd.o(.text.LCD_ShowString)
    [Anonymous Symbol]                       0x0000315a   Section        0  lcd_init.o(.text.LCD_WR_DATA)
    [Anonymous Symbol]                       0x00003178   Section        0  lcd_init.o(.text.LCD_WR_DATA8)
    [Anonymous Symbol]                       0x0000318c   Section        0  lcd_init.o(.text.LCD_WR_REG)
    [Anonymous Symbol]                       0x000031b8   Section        0  lcd_init.o(.text.LCD_Writ_Bus)
    [Anonymous Symbol]                       0x0000322c   Section        0  led.o(.text.LED_Flash)
    __arm_cp.6_0                             0x0000326c   Number         4  led.o(.text.LED_Flash)
    [Anonymous Symbol]                       0x00003270   Section        0  led.o(.text.LED_ON)
    [Anonymous Symbol]                       0x00003280   Section        0  led.o(.text.LED_Toggle)
    [Anonymous Symbol]                       0x00003290   Section        0  oled.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x000032ec   Section        0  oled.o(.text.OLED_DrawPoint)
    [Anonymous Symbol]                       0x00003378   Section        0  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x0000345c   Section        0  oled.o(.text.OLED_RST_Clr)
    [Anonymous Symbol]                       0x0000346c   Section        0  oled.o(.text.OLED_RST_Set)
    [Anonymous Symbol]                       0x0000347c   Section        0  oled.o(.text.OLED_RS_Clr)
    [Anonymous Symbol]                       0x0000348c   Section        0  oled.o(.text.OLED_RS_Set)
    __arm_cp.2_0                             0x0000349c   Number         4  oled.o(.text.OLED_RS_Set)
    [Anonymous Symbol]                       0x000034a0   Section        0  oled.o(.text.OLED_Refresh_Gram)
    __arm_cp.0_0                             0x00003518   Number         4  oled.o(.text.OLED_Refresh_Gram)
    [Anonymous Symbol]                       0x0000351c   Section        0  oled.o(.text.OLED_SCLK_Clr)
    [Anonymous Symbol]                       0x0000352c   Section        0  oled.o(.text.OLED_SCLK_Set)
    [Anonymous Symbol]                       0x0000353c   Section        0  oled.o(.text.OLED_SDIN_Clr)
    [Anonymous Symbol]                       0x0000354c   Section        0  oled.o(.text.OLED_SDIN_Set)
    __arm_cp.5_0                             0x0000355c   Number         4  oled.o(.text.OLED_SDIN_Set)
    [Anonymous Symbol]                       0x00003560   Section        0  oled.o(.text.OLED_ShowChar)
    __arm_cp.12_0                            0x0000366c   Number         4  oled.o(.text.OLED_ShowChar)
    __arm_cp.12_1                            0x00003670   Number         4  oled.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x00003674   Section        0  oled.o(.text.OLED_ShowNumber)
    [Anonymous Symbol]                       0x00003750   Section        0  oled.o(.text.OLED_ShowString)
    [Anonymous Symbol]                       0x000037ca   Section        0  oled.o(.text.OLED_WR_Byte)
    [Anonymous Symbol]                       0x00003838   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init)
    __arm_cp.8_0                             0x00003898   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init)
    __arm_cp.8_1                             0x0000389c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init)
    [Anonymous Symbol]                       0x000038a0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.55_0                            0x000038b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.55_1                            0x000038b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    [Anonymous Symbol]                       0x000038b8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    [Anonymous Symbol]                       0x000038c0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x000039e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x000039e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000039ec   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_0                             0x00003a60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_2                             0x00003a64   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00003a68   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00003aac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00003ab0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00003ac0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_0                             0x00003ae8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_2                             0x00003aec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00003af0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.6_0                             0x00003b40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.6_2                             0x00003b44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00003b48   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.7_0                             0x00003b9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.7_2                             0x00003ba0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x00003ba4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00003bd8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00003bdc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00003c4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00003c50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00003c54   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00003c58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00003c5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00003c60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00003c64   Section        0  motor.o(.text.Set_PWM)
    __arm_cp.0_0                             0x00003dc4   Number         4  motor.o(.text.Set_PWM)
    __arm_cp.0_1                             0x00003dc8   Number         4  motor.o(.text.Set_PWM)
    [Anonymous Symbol]                       0x00003dcc   Section        0  board.o(.text.Systick_getTick)
    __arm_cp.4_0                             0x00003dd4   Number         4  board.o(.text.Systick_getTick)
    [Anonymous Symbol]                       0x00003dd8   Section        0  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_0                             0x00003eec   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_1                             0x00003ef0   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_2                             0x00003ef4   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_3                             0x00003ef8   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_4                             0x00003efc   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_5                             0x00003f00   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_6                             0x00003f04   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_7                             0x00003f08   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_8                             0x00003f0c   Number         4  control.o(.text.TIMG0_IRQHandler)
    __arm_cp.0_9                             0x00003f10   Number         4  control.o(.text.TIMG0_IRQHandler)
    [Anonymous Symbol]                       0x00003f14   Section        0  uart_callback.o(.text.UART1_IRQHandler)
    __arm_cp.9_0                             0x00003f50   Number         4  uart_callback.o(.text.UART1_IRQHandler)
    __arm_cp.9_1                             0x00003f54   Number         4  uart_callback.o(.text.UART1_IRQHandler)
    __arm_cp.9_2                             0x00003f58   Number         4  uart_callback.o(.text.UART1_IRQHandler)
    __NVIC_ClearPendingIRQ                   0x00003f5d   Thumb Code    40  empty.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00003f5c   Section        0  empty.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.1_0                             0x00003f84   Number         4  empty.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x00003f89   Thumb Code    40  empty.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00003f88   Section        0  empty.o(.text.__NVIC_EnableIRQ)
    __arm_cp.2_0                             0x00003fb0   Number         4  empty.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x00003fb5   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00003fb4   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.36_0                            0x00004030   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.36_1                            0x00004034   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00004038   Section        0  uart_callback.o(.text.bt_control)
    __arm_cp.8_0                             0x0000425c   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_1                             0x00004260   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_2                             0x00004264   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_3                             0x00004268   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_4                             0x0000426c   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_5                             0x00004270   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_6                             0x00004274   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_7                             0x00004278   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_8                             0x0000427c   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_9                             0x00004280   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_10                            0x00004284   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_11                            0x00004288   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_12                            0x0000428c   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_13                            0x00004290   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_14                            0x00004294   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_15                            0x00004298   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_16                            0x0000429c   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_17                            0x000042a0   Number         4  uart_callback.o(.text.bt_control)
    __arm_cp.8_18                            0x000042a4   Number         4  uart_callback.o(.text.bt_control)
    [Anonymous Symbol]                       0x000042a8   Section        0  board.o(.text.delay_ms)
    __arm_cp.5_0                             0x000042d4   Number         4  board.o(.text.delay_ms)
    [Anonymous Symbol]                       0x000042d8   Section        0  board.o(.text.delay_us)
    __arm_cp.6_0                             0x0000435c   Number         4  board.o(.text.delay_us)
    __arm_cp.6_1                             0x00004360   Number         4  board.o(.text.delay_us)
    __arm_cp.6_2                             0x00004364   Number         4  board.o(.text.delay_us)
    [Anonymous Symbol]                       0x00004368   Section        0  board.o(.text.fputc)
    __arm_cp.0_0                             0x00004390   Number         4  board.o(.text.fputc)
    [Anonymous Symbol]                       0x00004394   Section        0  key.o(.text.keyValue)
    __arm_cp.0_0                             0x000043b0   Number         4  key.o(.text.keyValue)
    [Anonymous Symbol]                       0x000043b4   Section        0  key.o(.text.key_scan)
    __arm_cp.2_1                             0x00004518   Number         4  key.o(.text.key_scan)
    __arm_cp.2_2                             0x0000451c   Number         4  key.o(.text.key_scan)
    __arm_cp.2_3                             0x00004520   Number         4  key.o(.text.key_scan)
    __arm_cp.2_4                             0x00004524   Number         4  key.o(.text.key_scan)
    [Anonymous Symbol]                       0x00004528   Section        0  key.o(.text.lapsKeyValue)
    __arm_cp.3_0                             0x00004544   Number         4  key.o(.text.lapsKeyValue)
    [Anonymous Symbol]                       0x00004548   Section        0  key.o(.text.laps_key_scan)
    __arm_cp.4_0                             0x000046ac   Number         4  key.o(.text.laps_key_scan)
    __arm_cp.4_1                             0x000046b0   Number         4  key.o(.text.laps_key_scan)
    __arm_cp.4_2                             0x000046b4   Number         4  key.o(.text.laps_key_scan)
    __arm_cp.4_3                             0x000046b8   Number         4  key.o(.text.laps_key_scan)
    __arm_cp.4_4                             0x000046bc   Number         4  key.o(.text.laps_key_scan)
    __arm_cp.4_5                             0x000046c0   Number         4  key.o(.text.laps_key_scan)
    __arm_cp.4_6                             0x000046c4   Number         4  key.o(.text.laps_key_scan)
    __arm_cp.4_7                             0x000046c8   Number         4  key.o(.text.laps_key_scan)
    [Anonymous Symbol]                       0x000046cc   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00004748   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x0000474c   Section        0  control.o(.text.myabs)
    [Anonymous Symbol]                       0x0000476c   Section        0  lcd.o(.text.mypow)
    [Anonymous Symbol]                       0x0000479c   Section        0  oled.o(.text.oled_pow)
    [Anonymous Symbol]                       0x000047cc   Section        0  show.o(.text.oled_show)
    __arm_cp.0_38                            0x00004b60   Number         4  show.o(.text.oled_show)
    __arm_cp.0_39                            0x00004b64   Number         4  show.o(.text.oled_show)
    __arm_cp.0_40                            0x00004b68   Number         4  show.o(.text.oled_show)
    __arm_cp.0_41                            0x00004b6c   Number         4  show.o(.text.oled_show)
    __arm_cp.0_42                            0x00004b70   Number         4  show.o(.text.oled_show)
    __arm_cp.0_43                            0x00004b74   Number         4  show.o(.text.oled_show)
    __arm_cp.0_44                            0x00004b78   Number         4  show.o(.text.oled_show)
    __arm_cp.0_45                            0x00004b7c   Number         4  show.o(.text.oled_show)
    __arm_cp.0_8                             0x00004c54   Number         4  show.o(.text.oled_show)
    __arm_cp.0_9                             0x00004c58   Number         4  show.o(.text.oled_show)
    __arm_cp.0_10                            0x00004c5c   Number         4  show.o(.text.oled_show)
    __arm_cp.0_11                            0x00004c60   Number         4  show.o(.text.oled_show)
    __arm_cp.0_12                            0x00004c64   Number         4  show.o(.text.oled_show)
    __arm_cp.0_13                            0x00004c68   Number         4  show.o(.text.oled_show)
    __arm_cp.0_14                            0x00004c6c   Number         4  show.o(.text.oled_show)
    __arm_cp.0_15                            0x00004c70   Number         4  show.o(.text.oled_show)
    __arm_cp.0_16                            0x00004c74   Number         4  show.o(.text.oled_show)
    __arm_cp.0_17                            0x00004c78   Number         4  show.o(.text.oled_show)
    __arm_cp.0_18                            0x00004c7c   Number         4  show.o(.text.oled_show)
    __arm_cp.0_19                            0x00004c80   Number         4  show.o(.text.oled_show)
    __arm_cp.0_20                            0x00004c84   Number         4  show.o(.text.oled_show)
    __arm_cp.0_21                            0x00004c88   Number         4  show.o(.text.oled_show)
    __arm_cp.0_22                            0x00004c8c   Number         4  show.o(.text.oled_show)
    __arm_cp.0_23                            0x00004c90   Number         4  show.o(.text.oled_show)
    __arm_cp.0_24                            0x00004c94   Number         4  show.o(.text.oled_show)
    __arm_cp.0_25                            0x00004c98   Number         4  show.o(.text.oled_show)
    __arm_cp.0_26                            0x00004c9c   Number         4  show.o(.text.oled_show)
    __arm_cp.0_27                            0x00004ca0   Number         4  show.o(.text.oled_show)
    __arm_cp.0_28                            0x00004ca4   Number         4  show.o(.text.oled_show)
    __arm_cp.0_29                            0x00004ca8   Number         4  show.o(.text.oled_show)
    __arm_cp.0_30                            0x00004cac   Number         4  show.o(.text.oled_show)
    __arm_cp.0_31                            0x00004cb0   Number         4  show.o(.text.oled_show)
    __arm_cp.0_32                            0x00004cb4   Number         4  show.o(.text.oled_show)
    __arm_cp.0_33                            0x00004cb8   Number         4  show.o(.text.oled_show)
    __arm_cp.0_34                            0x00004cbc   Number         4  show.o(.text.oled_show)
    __arm_cp.0_35                            0x00004cc0   Number         4  show.o(.text.oled_show)
    __arm_cp.0_36                            0x00004cc4   Number         4  show.o(.text.oled_show)
    __arm_cp.0_37                            0x00004cc8   Number         4  show.o(.text.oled_show)
    i.__0printf                              0x00004ccc   Section        0  printfa.o(i.__0printf)
    i.__ARM_clz                              0x00004cec   Section        0  depilogue.o(i.__ARM_clz)
    i.__ARM_fpclassify                       0x00004d1c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x00004d48   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x00004df4   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x00004e08   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x00004e10   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x00004e20   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x00004e34   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x00004e48   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00004e58   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00004e60   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x00004e70   Section        0  errno.o(i.__set_errno)
    _fp_digits                               0x00004e7d   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x00004e7c   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x00004ff1   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x00004ff0   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x000056dd   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x000056dc   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x000056fd   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x000056fc   Section        0  printfa.o(i._printf_pre_padding)
    i.pow                                    0x00005728   Section        0  pow.o(i.pow)
    i.sqrt                                   0x000060fc   Section        0  sqrt.o(i.sqrt)
    bp                                       0x00006148   Data          16  pow.o(.constdata)
    .constdata                               0x00006148   Section      136  pow.o(.constdata)
    dp_h                                     0x00006158   Data          16  pow.o(.constdata)
    dp_l                                     0x00006168   Data          16  pow.o(.constdata)
    L                                        0x00006178   Data          48  pow.o(.constdata)
    P                                        0x000061a8   Data          40  pow.o(.constdata)
    .constdata                               0x000061d0   Section        8  qnan.o(.constdata)
    gADC12_VOLTAGEClockConfig                0x000095cc   Data           8  ti_msp_dl_config.o(.rodata.gADC12_VOLTAGEClockConfig)
    [Anonymous Symbol]                       0x000095cc   Section        0  ti_msp_dl_config.o(.rodata.gADC12_VOLTAGEClockConfig)
    gDMA_CH0Config                           0x000095d4   Data          24  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    [Anonymous Symbol]                       0x000095d4   Section        0  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    gPWM_0ClockConfig                        0x000095ec   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x000095ec   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x000095f0   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x000095f0   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gSYSPLLConfig                            0x000095f8   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x000095f8   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_0ClockConfig                      0x00009620   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00009620   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x00009624   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x00009624   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x00009638   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00009638   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x0000963a   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x0000963a   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_1ClockConfig                       0x00009644   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x00009644   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x00009646   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x00009646   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x0000a0b4   Section        0  lcd.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0000a117   Section        0  control.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0000a14d   Section        0  show.o(.rodata.str1.1)
    .data                                    0x20200000   Section        4  stdout.o(.data)
    _errno                                   0x20200004   Data           4  errno.o(.data)
    .data                                    0x20200004   Section        4  errno.o(.data)
    APP_Show.flag                            0x20200038   Data           1  show.o(.bss.APP_Show.flag)
    [Anonymous Symbol]                       0x20200038   Section        0  show.o(.bss.APP_Show.flag)
    BTBufferHandler.handleSize               0x20200039   Data           1  uart_callback.o(.bss.BTBufferHandler.handleSize)
    [Anonymous Symbol]                       0x20200039   Section        0  uart_callback.o(.bss.BTBufferHandler.handleSize)
    BTBufferHandler.handleflag               0x2020003a   Data           1  uart_callback.o(.bss.BTBufferHandler.handleflag)
    [Anonymous Symbol]                       0x2020003a   Section        0  uart_callback.o(.bss.BTBufferHandler.handleflag)
    BTBufferHandler.lastSize                 0x2020003b   Data           1  uart_callback.o(.bss.BTBufferHandler.lastSize)
    [Anonymous Symbol]                       0x2020003b   Section        0  uart_callback.o(.bss.BTBufferHandler.lastSize)
    IRDM_line_inspection.corner_detect_delay 0x20200044   Data           4  ir_module.o(.bss.IRDM_line_inspection.corner_detect_delay)
    [Anonymous Symbol]                       0x20200044   Section        0  ir_module.o(.bss.IRDM_line_inspection.corner_detect_delay)
    IRDM_line_inspection.last_state          0x20200048   Data           4  ir_module.o(.bss.IRDM_line_inspection.last_state)
    [Anonymous Symbol]                       0x20200048   Section        0  ir_module.o(.bss.IRDM_line_inspection.last_state)
    IRDM_line_inspection.ten_time            0x2020004c   Data           4  ir_module.o(.bss.IRDM_line_inspection.ten_time)
    [Anonymous Symbol]                       0x2020004c   Section        0  ir_module.o(.bss.IRDM_line_inspection.ten_time)
    IRDM_line_inspection.turn_cooldown       0x20200050   Data           4  ir_module.o(.bss.IRDM_line_inspection.turn_cooldown)
    [Anonymous Symbol]                       0x20200050   Section        0  ir_module.o(.bss.IRDM_line_inspection.turn_cooldown)
    IRDM_line_inspection.turn_counter        0x20200054   Data           4  ir_module.o(.bss.IRDM_line_inspection.turn_counter)
    [Anonymous Symbol]                       0x20200054   Section        0  ir_module.o(.bss.IRDM_line_inspection.turn_counter)
    IRDM_line_inspection.turn_phase          0x20200058   Data           4  ir_module.o(.bss.IRDM_line_inspection.turn_phase)
    [Anonymous Symbol]                       0x20200058   Section        0  ir_module.o(.bss.IRDM_line_inspection.turn_phase)
    Incremental_PI_Left.Bias                 0x2020005c   Data           4  control.o(.bss.Incremental_PI_Left.Bias)
    [Anonymous Symbol]                       0x2020005c   Section        0  control.o(.bss.Incremental_PI_Left.Bias)
    Incremental_PI_Left.Last_bias            0x20200060   Data           4  control.o(.bss.Incremental_PI_Left.Last_bias)
    [Anonymous Symbol]                       0x20200060   Section        0  control.o(.bss.Incremental_PI_Left.Last_bias)
    Incremental_PI_Left.Pwm                  0x20200064   Data           4  control.o(.bss.Incremental_PI_Left.Pwm)
    [Anonymous Symbol]                       0x20200064   Section        0  control.o(.bss.Incremental_PI_Left.Pwm)
    Incremental_PI_Right.Bias                0x20200068   Data           4  control.o(.bss.Incremental_PI_Right.Bias)
    [Anonymous Symbol]                       0x20200068   Section        0  control.o(.bss.Incremental_PI_Right.Bias)
    Incremental_PI_Right.Last_bias           0x2020006c   Data           4  control.o(.bss.Incremental_PI_Right.Last_bias)
    [Anonymous Symbol]                       0x2020006c   Section        0  control.o(.bss.Incremental_PI_Right.Last_bias)
    Incremental_PI_Right.Pwm                 0x20200070   Data           4  control.o(.bss.Incremental_PI_Right.Pwm)
    [Anonymous Symbol]                       0x20200070   Section        0  control.o(.bss.Incremental_PI_Right.Pwm)
    LED_Flash.temp                           0x20200074   Data           2  led.o(.bss.LED_Flash.temp)
    [Anonymous Symbol]                       0x20200074   Section        0  led.o(.bss.LED_Flash.temp)
    bt_control.Data                          0x202004bc   Data           4  uart_callback.o(.bss.bt_control.Data)
    [Anonymous Symbol]                       0x202004bc   Section        0  uart_callback.o(.bss.bt_control.Data)
    bt_control.Flag_PID                      0x202004c0   Data           1  uart_callback.o(.bss.bt_control.Flag_PID)
    [Anonymous Symbol]                       0x202004c0   Section        0  uart_callback.o(.bss.bt_control.Flag_PID)
    bt_control.Receive                       0x202004c1   Data          50  uart_callback.o(.bss.bt_control.Receive)
    [Anonymous Symbol]                       0x202004c1   Section        0  uart_callback.o(.bss.bt_control.Receive)
    bt_control.Usart_Receive                 0x202004f4   Data           4  uart_callback.o(.bss.bt_control.Usart_Receive)
    [Anonymous Symbol]                       0x202004f4   Section        0  uart_callback.o(.bss.bt_control.Usart_Receive)
    bt_control.i                             0x202004f8   Data           1  uart_callback.o(.bss.bt_control.i)
    [Anonymous Symbol]                       0x202004f8   Section        0  uart_callback.o(.bss.bt_control.i)
    bt_control.j                             0x202004f9   Data           1  uart_callback.o(.bss.bt_control.j)
    [Anonymous Symbol]                       0x202004f9   Section        0  uart_callback.o(.bss.bt_control.j)
    key_scan.check_once                      0x2020069c   Data           1  key.o(.bss.key_scan.check_once)
    [Anonymous Symbol]                       0x2020069c   Section        0  key.o(.bss.key_scan.check_once)
    key_scan.long_press_time                 0x2020069e   Data           2  key.o(.bss.key_scan.long_press_time)
    [Anonymous Symbol]                       0x2020069e   Section        0  key.o(.bss.key_scan.long_press_time)
    key_scan.press_flag                      0x202006a0   Data           1  key.o(.bss.key_scan.press_flag)
    [Anonymous Symbol]                       0x202006a0   Section        0  key.o(.bss.key_scan.press_flag)
    key_scan.time_core                       0x202006a2   Data           2  key.o(.bss.key_scan.time_core)
    [Anonymous Symbol]                       0x202006a2   Section        0  key.o(.bss.key_scan.time_core)
    laps_key_scan.check_once                 0x202006a4   Data           1  key.o(.bss.laps_key_scan.check_once)
    [Anonymous Symbol]                       0x202006a4   Section        0  key.o(.bss.laps_key_scan.check_once)
    laps_key_scan.long_press_time            0x202006a6   Data           2  key.o(.bss.laps_key_scan.long_press_time)
    [Anonymous Symbol]                       0x202006a6   Section        0  key.o(.bss.laps_key_scan.long_press_time)
    laps_key_scan.press_flag                 0x202006a8   Data           1  key.o(.bss.laps_key_scan.press_flag)
    [Anonymous Symbol]                       0x202006a8   Section        0  key.o(.bss.laps_key_scan.press_flag)
    laps_key_scan.time_core                  0x202006aa   Data           2  key.o(.bss.laps_key_scan.time_core)
    [Anonymous Symbol]                       0x202006aa   Section        0  key.o(.bss.laps_key_scan.time_core)
    STACK                                    0x202006b0   Section     4096  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000000e1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memset                           0x000000e9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000000f7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000000f7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000000f7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000000fb   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x0000010d   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x000001af   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x000001b7   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x000001bf   Thumb Code   122  fmul.o(.text)
    __aeabi_fdiv                             0x00000239   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x000002b5   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x000003fd   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x00000409   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00000419   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x000004e9   Thumb Code   234  ddiv.o(.text)
    __aeabi_fcmple                           0x000005d9   Thumb Code    28  fcmple.o(.text)
    __aeabi_fcmplt                           0x000005f5   Thumb Code    28  fcmplt.o(.text)
    __aeabi_fcmpge                           0x00000611   Thumb Code    28  fcmpge.o(.text)
    __aeabi_i2f                              0x0000062d   Thumb Code    22  fflti.o(.text)
    __aeabi_ui2f                             0x00000643   Thumb Code    14  ffltui.o(.text)
    __aeabi_i2d                              0x00000651   Thumb Code    34  dflti.o(.text)
    __aeabi_f2iz                             0x00000679   Thumb Code    50  ffixi.o(.text)
    __aeabi_d2iz                             0x000006ad   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x000006f5   Thumb Code    40  f2d.o(.text)
    __aeabi_d2f                              0x0000071d   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x00000755   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x00000755   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x00000793   Thumb Code    96  uldiv.o(.text)
    __aeabi_llsl                             0x000007f3   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x000007f3   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x00000813   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x00000813   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x00000835   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x00000835   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x0000085b   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0000085b   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x0000086b   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x000008dd   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x000008f7   Thumb Code   164  depilogue.o(.text)
    __ARM_scalbn                             0x0000099b   Thumb Code    44  dscalb.o(.text)
    scalbn                                   0x0000099b   Thumb Code     0  dscalb.o(.text)
    __aeabi_d2ulz                            0x000009c9   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x00000a09   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00000a31   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000a31   Thumb Code     0  init.o(.text)
    _dsqrt                                   0x00000a61   Thumb Code   162  dsqrt.o(.text)
    ADC1_IRQHandler                          0x00000b05   Thumb Code    28  adc.o(.text.ADC1_IRQHandler)
    APP_Show                                 0x00000b29   Thumb Code   340  show.o(.text.APP_Show)
    BTBufferHandler                          0x00000ccd   Thumb Code   188  uart_callback.o(.text.BTBufferHandler)
    BT_DAMConfig                             0x00000d99   Thumb Code    60  uart_callback.o(.text.BT_DAMConfig)
    DL_ADC12_setClockConfig                  0x00000f35   Thumb Code    64  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x00000fb1   Thumb Code    20  dl_common.o(.text.DL_Common_delayCycles)
    DL_DMA_initChannel                       0x0000114d   Thumb Code    70  dl_dma.o(.text.DL_DMA_initChannel)
    DL_SYSCTL_configSYSPLL                   0x00001479   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x000015c1   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_TimerA_initPWMMode                    0x0000161d   Thumb Code   234  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_initPWMMode                     0x00001765   Thumb Code   260  dl_timer.o(.text.DL_Timer_initPWMMode)
    DL_Timer_initTimerMode                   0x00001871   Thumb Code   252  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00001991   Thumb Code    48  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareAction         0x000019c1   Thumb Code    44  dl_timer.o(.text.DL_Timer_setCaptureCompareAction)
    DL_Timer_setCaptureCompareCtl            0x000019f5   Thumb Code    52  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    DL_Timer_setCaptureCompareInput          0x00001a31   Thumb Code    52  dl_timer.o(.text.DL_Timer_setCaptureCompareInput)
    DL_Timer_setCaptureCompareOutCtl         0x00001a65   Thumb Code    60  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00001aa5   Thumb Code    40  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00001ad1   Thumb Code    44  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00001bf1   Thumb Code    68  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00001cdd   Thumb Code    32  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_transmitDataBlocking             0x00001d83   Thumb Code    40  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    GROUP1_IRQHandler                        0x00001dad   Thumb Code   264  encoder.o(.text.GROUP1_IRQHandler)
    Get_Target_Encoder                       0x00001ecd   Thumb Code   112  control.o(.text.Get_Target_Encoder)
    Get_Velocity_From_Encoder                0x00001f45   Thumb Code   128  control.o(.text.Get_Velocity_From_Encoder)
    Get_battery_volt                         0x00001fe1   Thumb Code    76  adc.o(.text.Get_battery_volt)
    IRDM_line_inspection                     0x00002045   Thumb Code  1092  ir_module.o(.text.IRDM_line_inspection)
    Incremental_PI_Left                      0x000024e9   Thumb Code   156  control.o(.text.Incremental_PI_Left)
    Incremental_PI_Right                     0x00002591   Thumb Code   156  control.o(.text.Incremental_PI_Right)
    Key                                      0x00002649   Thumb Code   120  control.o(.text.Key)
    LCD_Address_Set                          0x000026cd   Thumb Code    82  lcd_init.o(.text.LCD_Address_Set)
    LCD_Display_Status                       0x00002721   Thumb Code   520  lcd.o(.text.LCD_Display_Status)
    LCD_DrawLine                             0x00002965   Thumb Code   296  lcd.o(.text.LCD_DrawLine)
    LCD_DrawPoint                            0x00002a8d   Thumb Code    44  lcd.o(.text.LCD_DrawPoint)
    LCD_DrawRectangle                        0x00002ab9   Thumb Code   138  lcd.o(.text.LCD_DrawRectangle)
    LCD_Fill                                 0x00002b43   Thumb Code   132  lcd.o(.text.LCD_Fill)
    LCD_GPIO_Init                            0x00002bc9   Thumb Code    44  lcd_init.o(.text.LCD_GPIO_Init)
    LCD_Init                                 0x00002bf5   Thumb Code   444  lcd_init.o(.text.LCD_Init)
    LCD_ShowChar                             0x00002db5   Thumb Code   516  lcd.o(.text.LCD_ShowChar)
    LCD_ShowIntNum                           0x00002fc9   Thumb Code   280  lcd.o(.text.LCD_ShowIntNum)
    LCD_ShowString                           0x000030e1   Thumb Code   122  lcd.o(.text.LCD_ShowString)
    LCD_WR_DATA                              0x0000315b   Thumb Code    30  lcd_init.o(.text.LCD_WR_DATA)
    LCD_WR_DATA8                             0x00003179   Thumb Code    20  lcd_init.o(.text.LCD_WR_DATA8)
    LCD_WR_REG                               0x0000318d   Thumb Code    44  lcd_init.o(.text.LCD_WR_REG)
    LCD_Writ_Bus                             0x000031b9   Thumb Code   116  lcd_init.o(.text.LCD_Writ_Bus)
    LED_Flash                                0x0000322d   Thumb Code    64  led.o(.text.LED_Flash)
    LED_ON                                   0x00003271   Thumb Code    16  led.o(.text.LED_ON)
    LED_Toggle                               0x00003281   Thumb Code    16  led.o(.text.LED_Toggle)
    OLED_Clear                               0x00003291   Thumb Code    92  oled.o(.text.OLED_Clear)
    OLED_DrawPoint                           0x000032ed   Thumb Code   140  oled.o(.text.OLED_DrawPoint)
    OLED_Init                                0x00003379   Thumb Code   228  oled.o(.text.OLED_Init)
    OLED_RST_Clr                             0x0000345d   Thumb Code    16  oled.o(.text.OLED_RST_Clr)
    OLED_RST_Set                             0x0000346d   Thumb Code    16  oled.o(.text.OLED_RST_Set)
    OLED_RS_Clr                              0x0000347d   Thumb Code    16  oled.o(.text.OLED_RS_Clr)
    OLED_RS_Set                              0x0000348d   Thumb Code    16  oled.o(.text.OLED_RS_Set)
    OLED_Refresh_Gram                        0x000034a1   Thumb Code   120  oled.o(.text.OLED_Refresh_Gram)
    OLED_SCLK_Clr                            0x0000351d   Thumb Code    16  oled.o(.text.OLED_SCLK_Clr)
    OLED_SCLK_Set                            0x0000352d   Thumb Code    16  oled.o(.text.OLED_SCLK_Set)
    OLED_SDIN_Clr                            0x0000353d   Thumb Code    16  oled.o(.text.OLED_SDIN_Clr)
    OLED_SDIN_Set                            0x0000354d   Thumb Code    16  oled.o(.text.OLED_SDIN_Set)
    OLED_ShowChar                            0x00003561   Thumb Code   268  oled.o(.text.OLED_ShowChar)
    OLED_ShowNumber                          0x00003675   Thumb Code   220  oled.o(.text.OLED_ShowNumber)
    OLED_ShowString                          0x00003751   Thumb Code   122  oled.o(.text.OLED_ShowString)
    OLED_WR_Byte                             0x000037cb   Thumb Code   110  oled.o(.text.OLED_WR_Byte)
    SYSCFG_DL_ADC12_VOLTAGE_init             0x00003839   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init)
    SYSCFG_DL_DMA_CH0_init                   0x000038a1   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    SYSCFG_DL_DMA_init                       0x000038b9   Thumb Code     8  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    SYSCFG_DL_GPIO_init                      0x000038c1   Thumb Code   292  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_PWM_0_init                     0x000039ed   Thumb Code   116  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_SYSCTL_init                    0x00003a69   Thumb Code    68  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00003ab1   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00003ac1   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x00003af1   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_1_init                    0x00003b49   Thumb Code    84  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_init                           0x00003ba5   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00003bdd   Thumb Code   112  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Set_PWM                                  0x00003c65   Thumb Code   352  motor.o(.text.Set_PWM)
    Systick_getTick                          0x00003dcd   Thumb Code     8  board.o(.text.Systick_getTick)
    TIMG0_IRQHandler                         0x00003dd9   Thumb Code   276  control.o(.text.TIMG0_IRQHandler)
    UART1_IRQHandler                         0x00003f15   Thumb Code    60  uart_callback.o(.text.UART1_IRQHandler)
    bt_control                               0x00004039   Thumb Code   548  uart_callback.o(.text.bt_control)
    delay_ms                                 0x000042a9   Thumb Code    44  board.o(.text.delay_ms)
    delay_us                                 0x000042d9   Thumb Code   132  board.o(.text.delay_us)
    fputc                                    0x00004369   Thumb Code    40  board.o(.text.fputc)
    keyValue                                 0x00004395   Thumb Code    28  key.o(.text.keyValue)
    key_scan                                 0x000043b5   Thumb Code   356  key.o(.text.key_scan)
    lapsKeyValue                             0x00004529   Thumb Code    28  key.o(.text.lapsKeyValue)
    laps_key_scan                            0x00004549   Thumb Code   356  key.o(.text.laps_key_scan)
    main                                     0x000046cd   Thumb Code   124  empty.o(.text.main)
    myabs                                    0x0000474d   Thumb Code    32  control.o(.text.myabs)
    mypow                                    0x0000476d   Thumb Code    48  lcd.o(.text.mypow)
    oled_pow                                 0x0000479d   Thumb Code    48  oled.o(.text.oled_pow)
    oled_show                                0x000047cd   Thumb Code  1160  show.o(.text.oled_show)
    __0printf                                0x00004ccd   Thumb Code    24  printfa.o(i.__0printf)
    __1printf                                0x00004ccd   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x00004ccd   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x00004ccd   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x00004ccd   Thumb Code     0  printfa.o(i.__0printf)
    __ARM_clz                                0x00004ced   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __ARM_fpclassify                         0x00004d1d   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x00004d49   Thumb Code   172  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x00004df5   Thumb Code    16  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x00004e09   Thumb Code     8  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x00004e11   Thumb Code    16  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x00004e21   Thumb Code    16  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x00004e35   Thumb Code    14  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x00004e49   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00004e59   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00004e61   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x00004e71   Thumb Code     6  errno.o(i.__set_errno)
    pow                                      0x00005729   Thumb Code  2506  pow.o(i.pow)
    sqrt                                     0x000060fd   Thumb Code    66  sqrt.o(i.sqrt)
    __mathlib_zero                           0x000061d0   Data           8  qnan.o(.constdata)
    ascii_1206                               0x000061d8   Data        1140  lcd.o(.rodata.ascii_1206)
    ascii_1608                               0x0000664c   Data        1520  lcd.o(.rodata.ascii_1608)
    ascii_2412                               0x00006c3c   Data        4560  lcd.o(.rodata.ascii_2412)
    ascii_3216                               0x00007e0c   Data        6080  lcd.o(.rodata.ascii_3216)
    oled_asc2_1206                           0x00009650   Data        1140  oled.o(.rodata.oled_asc2_1206)
    oled_asc2_1608                           0x00009ac4   Data        1520  oled.o(.rodata.oled_asc2_1608)
    Region$$Table$$Base                      0x0000a1d4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0000a1f4   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20200000   Data           4  stdout.o(.data)
    Car_Mode                                 0x20200008   Data           1  empty.o(.data.Car_Mode)
    Flag_Stop                                0x20200009   Data           1  control.o(.data.Flag_Stop)
    RC_Velocity                              0x2020000c   Data           4  empty.o(.data.RC_Velocity)
    Run_Mode                                 0x20200010   Data           4  control.o(.data.Run_Mode)
    Turn90Angle                              0x20200014   Data           4  ir_module.o(.data.Turn90Angle)
    Velocity_KI                              0x20200018   Data           4  control.o(.data.Velocity_KI)
    Velocity_KP                              0x2020001c   Data           4  control.o(.data.Velocity_KP)
    maxTurnAngle                             0x20200020   Data           4  ir_module.o(.data.maxTurnAngle)
    midTurnAngle                             0x20200024   Data           4  ir_module.o(.data.midTurnAngle)
    minTurnAngle                             0x20200028   Data           4  ir_module.o(.data.minTurnAngle)
    sharpTurnAngle                           0x2020002c   Data           4  ir_module.o(.data.sharpTurnAngle)
    target_laps                              0x20200030   Data           1  empty.o(.data.target_laps)
    Get_Encoder_countA                       0x2020003c   Data           4  encoder.o(.bss.Get_Encoder_countA)
    Get_Encoder_countB                       0x20200040   Data           4  encoder.o(.bss.Get_Encoder_countB)
    MotorA                                   0x20200078   Data          16  control.o(.bss.MotorA)
    MotorB                                   0x20200088   Data          16  control.o(.bss.MotorB)
    Move_X                                   0x20200098   Data           4  empty.o(.bss.Move_X)
    Move_Z                                   0x2020009c   Data           4  empty.o(.bss.Move_Z)
    OLED_GRAM                                0x202000a0   Data        1024  oled.o(.bss.OLED_GRAM)
    OriginalEncoder                          0x202004a0   Data           8  control.o(.bss.OriginalEncoder)
    PID_Send                                 0x202004a8   Data           1  empty.o(.bss.PID_Send)
    Turn_Flag                                0x202004ac   Data           4  uart_callback.o(.bss.Turn_Flag)
    Velocity_Left                            0x202004b0   Data           4  empty.o(.bss.Velocity_Left)
    Velocity_Right                           0x202004b4   Data           4  empty.o(.bss.Velocity_Right)
    Voltage                                  0x202004b8   Data           4  empty.o(.bss.Voltage)
    corner_count                             0x202004fa   Data           1  empty.o(.bss.corner_count)
    current_laps                             0x202004fb   Data           1  empty.o(.bss.current_laps)
    gBTCounts                                0x202004fc   Data           1  uart_callback.o(.bss.gBTCounts)
    gBTPacket                                0x202004fd   Data         200  uart_callback.o(.bss.gBTPacket)
    gCheckADC                                0x202005c5   Data           1  adc.o(.bss.gCheckADC)
    gPWM_0Backup                             0x202005c8   Data         188  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    gpio_interrup1                           0x20200684   Data           4  encoder.o(.bss.gpio_interrup1)
    gpio_interrup2                           0x20200688   Data           4  encoder.o(.bss.gpio_interrup2)
    ir_dh1_state                             0x2020068c   Data           4  ir_module.o(.bss.ir_dh1_state)
    ir_dh2_state                             0x20200690   Data           4  ir_module.o(.bss.ir_dh2_state)
    ir_dh3_state                             0x20200694   Data           4  ir_module.o(.bss.ir_dh3_state)
    ir_dh4_state                             0x20200698   Data           4  ir_module.o(.bss.ir_dh4_state)
    __initial_sp                             0x202016b0   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x0000a230, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x0000a1f4, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO            3    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO         1733  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO         1830    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO         1833    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1835    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1837    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO         1838    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1840    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1842    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO         1831    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO            4    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000024   Code   RO         1743    .text               mc_p.l(memseta.o)
    0x0000010c   0x0000010c   0x000000b2   Code   RO         1775    .text               mf_p.l(fadd.o)
    0x000001be   0x000001be   0x0000007a   Code   RO         1777    .text               mf_p.l(fmul.o)
    0x00000238   0x00000238   0x0000007c   Code   RO         1779    .text               mf_p.l(fdiv.o)
    0x000002b4   0x000002b4   0x00000164   Code   RO         1781    .text               mf_p.l(dadd.o)
    0x00000418   0x00000418   0x000000d0   Code   RO         1783    .text               mf_p.l(dmul.o)
    0x000004e8   0x000004e8   0x000000f0   Code   RO         1785    .text               mf_p.l(ddiv.o)
    0x000005d8   0x000005d8   0x0000001c   Code   RO         1787    .text               mf_p.l(fcmple.o)
    0x000005f4   0x000005f4   0x0000001c   Code   RO         1789    .text               mf_p.l(fcmplt.o)
    0x00000610   0x00000610   0x0000001c   Code   RO         1791    .text               mf_p.l(fcmpge.o)
    0x0000062c   0x0000062c   0x00000016   Code   RO         1793    .text               mf_p.l(fflti.o)
    0x00000642   0x00000642   0x0000000e   Code   RO         1795    .text               mf_p.l(ffltui.o)
    0x00000650   0x00000650   0x00000028   Code   RO         1797    .text               mf_p.l(dflti.o)
    0x00000678   0x00000678   0x00000032   Code   RO         1799    .text               mf_p.l(ffixi.o)
    0x000006aa   0x000006aa   0x00000002   PAD
    0x000006ac   0x000006ac   0x00000048   Code   RO         1801    .text               mf_p.l(dfixi.o)
    0x000006f4   0x000006f4   0x00000028   Code   RO         1803    .text               mf_p.l(f2d.o)
    0x0000071c   0x0000071c   0x00000038   Code   RO         1805    .text               mf_p.l(d2f.o)
    0x00000754   0x00000754   0x0000003e   Code   RO         1848    .text               mc_p.l(uidiv_div0.o)
    0x00000792   0x00000792   0x00000060   Code   RO         1854    .text               mc_p.l(uldiv.o)
    0x000007f2   0x000007f2   0x00000020   Code   RO         1856    .text               mc_p.l(llshl.o)
    0x00000812   0x00000812   0x00000022   Code   RO         1858    .text               mc_p.l(llushr.o)
    0x00000834   0x00000834   0x00000026   Code   RO         1860    .text               mc_p.l(llsshr.o)
    0x0000085a   0x0000085a   0x00000000   Code   RO         1869    .text               mc_p.l(iusefp.o)
    0x0000085a   0x0000085a   0x00000082   Code   RO         1870    .text               mf_p.l(fepilogue.o)
    0x000008dc   0x000008dc   0x000000be   Code   RO         1872    .text               mf_p.l(depilogue.o)
    0x0000099a   0x0000099a   0x0000002c   Code   RO         1876    .text               mf_p.l(dscalb.o)
    0x000009c6   0x000009c6   0x00000002   PAD
    0x000009c8   0x000009c8   0x00000040   Code   RO         1878    .text               mf_p.l(dfixul.o)
    0x00000a08   0x00000a08   0x00000028   Code   RO         1880    .text               mf_p.l(cdrcmple.o)
    0x00000a30   0x00000a30   0x00000030   Code   RO         1882    .text               mc_p.l(init.o)
    0x00000a60   0x00000a60   0x000000a2   Code   RO         1884    .text               mf_p.l(dsqrt.o)
    0x00000b02   0x00000b02   0x00000002   PAD
    0x00000b04   0x00000b04   0x00000024   Code   RO         1459    .text.ADC1_IRQHandler  adc.o
    0x00000b28   0x00000b28   0x000001a4   Code   RO         1621    .text.APP_Show      show.o
    0x00000ccc   0x00000ccc   0x000000cc   Code   RO         1660    .text.BTBufferHandler  uart_callback.o
    0x00000d98   0x00000d98   0x00000048   Code   RO         1648    .text.BT_DAMConfig  uart_callback.o
    0x00000de0   0x00000de0   0x0000001c   Code   RO          115    .text.DL_ADC12_clearInterruptStatus  ti_msp_dl_config.o
    0x00000dfc   0x00000dfc   0x0000004a   Code   RO          111    .text.DL_ADC12_configConversionMem  ti_msp_dl_config.o
    0x00000e46   0x00000e46   0x00000016   Code   RO          119    .text.DL_ADC12_enableConversions  ti_msp_dl_config.o
    0x00000e5c   0x00000e5c   0x0000001c   Code   RO          117    .text.DL_ADC12_enableInterrupt  ti_msp_dl_config.o
    0x00000e78   0x00000e78   0x00000018   Code   RO           51    .text.DL_ADC12_enablePower  ti_msp_dl_config.o
    0x00000e90   0x00000e90   0x00000030   Code   RO         1457    .text.DL_ADC12_getMemResult  adc.o
    0x00000ec0   0x00000ec0   0x00000012   Code   RO         1461    .text.DL_ADC12_getPendingInterrupt  adc.o
    0x00000ed2   0x00000ed2   0x00000002   PAD
    0x00000ed4   0x00000ed4   0x00000048   Code   RO          109    .text.DL_ADC12_initSingleSample  ti_msp_dl_config.o
    0x00000f1c   0x00000f1c   0x00000018   Code   RO           43    .text.DL_ADC12_reset  ti_msp_dl_config.o
    0x00000f34   0x00000f34   0x00000048   Code   RO         1292    .text.DL_ADC12_setClockConfig  dl_adc12.o
    0x00000f7c   0x00000f7c   0x00000018   Code   RO          113    .text.DL_ADC12_setSampleTime0  ti_msp_dl_config.o
    0x00000f94   0x00000f94   0x0000001c   Code   RO         1455    .text.DL_ADC12_startConversion  adc.o
    0x00000fb0   0x00000fb0   0x00000014   Code   RO         1244    .text.DL_Common_delayCycles  dl_common.o
    0x00000fc4   0x00000fc4   0x00000028   Code   RO          127    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x00000fec   0x00000fec   0x00000028   Code   RO          226    .text.DL_Common_updateReg  dl_uart.o
    0x00001014   0x00001014   0x00000028   Code   RO          316    .text.DL_Common_updateReg  dl_timer.o
    0x0000103c   0x0000103c   0x00000028   Code   RO         1172    .text.DL_Common_updateReg  dl_dma.o
    0x00001064   0x00001064   0x00000028   Code   RO         1294    .text.DL_Common_updateReg  dl_adc12.o
    0x0000108c   0x0000108c   0x00000054   Code   RO         1168    .text.DL_DMA_configTransfer  dl_dma.o
    0x000010e0   0x000010e0   0x00000026   Code   RO         1650    .text.DL_DMA_disableChannel  uart_callback.o
    0x00001106   0x00001106   0x00000026   Code   RO         1658    .text.DL_DMA_enableChannel  uart_callback.o
    0x0000112c   0x0000112c   0x00000020   Code   RO         1662    .text.DL_DMA_getTransferSize  uart_callback.o
    0x0000114c   0x0000114c   0x00000046   Code   RO         1166    .text.DL_DMA_initChannel  dl_dma.o
    0x00001192   0x00001192   0x00000002   PAD
    0x00001194   0x00001194   0x00000028   Code   RO         1654    .text.DL_DMA_setDestAddr  uart_callback.o
    0x000011bc   0x000011bc   0x00000028   Code   RO         1652    .text.DL_DMA_setSrcAddr  uart_callback.o
    0x000011e4   0x000011e4   0x00000030   Code   RO         1656    .text.DL_DMA_setTransferSize  uart_callback.o
    0x00001214   0x00001214   0x00000038   Code   RO         1170    .text.DL_DMA_setTrigger  dl_dma.o
    0x0000124c   0x0000124c   0x00000018   Code   RO           67    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x00001264   0x00001264   0x0000001c   Code   RO         1440    .text.DL_GPIO_clearInterruptStatus  encoder.o
    0x00001280   0x00001280   0x00000014   Code   RO           63    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x00001294   0x00001294   0x00000014   Code   RO         1359    .text.DL_GPIO_clearPins  oled.o
    0x000012a8   0x000012a8   0x00000014   Code   RO         1401    .text.DL_GPIO_clearPins  led.o
    0x000012bc   0x000012bc   0x00000014   Code   RO         1423    .text.DL_GPIO_clearPins  motor.o
    0x000012d0   0x000012d0   0x00000014   Code   RO         1553    .text.DL_GPIO_clearPins  lcd_init.o
    0x000012e4   0x000012e4   0x0000001c   Code   RO           69    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x00001300   0x00001300   0x00000018   Code   RO           55    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00001318   0x00001318   0x00000018   Code   RO           45    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00001330   0x00001330   0x00000018   Code   RO         1436    .text.DL_GPIO_getEnabledInterruptStatus  encoder.o
    0x00001348   0x00001348   0x00000018   Code   RO           61    .text.DL_GPIO_initDigitalInput  ti_msp_dl_config.o
    0x00001360   0x00001360   0x00000014   Code   RO           59    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x00001374   0x00001374   0x0000001c   Code   RO           57    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x00001390   0x00001390   0x0000001c   Code   RO           53    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x000013ac   0x000013ac   0x00000016   Code   RO         1376    .text.DL_GPIO_readPins  key.o
    0x000013c2   0x000013c2   0x00000016   Code   RO         1438    .text.DL_GPIO_readPins  encoder.o
    0x000013d8   0x000013d8   0x00000016   Code   RO         1473    .text.DL_GPIO_readPins  ir_module.o
    0x000013ee   0x000013ee   0x00000002   PAD
    0x000013f0   0x000013f0   0x00000018   Code   RO           37    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00001408   0x00001408   0x00000014   Code   RO         1361    .text.DL_GPIO_setPins  oled.o
    0x0000141c   0x0000141c   0x00000014   Code   RO         1425    .text.DL_GPIO_setPins  motor.o
    0x00001430   0x00001430   0x00000018   Code   RO         1549    .text.DL_GPIO_setPins  lcd_init.o
    0x00001448   0x00001448   0x00000018   Code   RO           65    .text.DL_GPIO_setUpperPinsPolarity  ti_msp_dl_config.o
    0x00001460   0x00001460   0x00000018   Code   RO         1409    .text.DL_GPIO_togglePins  led.o
    0x00001478   0x00001478   0x000000c0   Code   RO         1697    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00001538   0x00001538   0x0000000c   Code   RO           77    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00001544   0x00001544   0x00000014   Code   RO           79    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x00001558   0x00001558   0x00000018   Code   RO           71    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x00001570   0x00001570   0x0000001c   Code   RO           73    .text.DL_SYSCTL_setFlashWaitState  ti_msp_dl_config.o
    0x0000158c   0x0000158c   0x0000001c   Code   RO           75    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x000015a8   0x000015a8   0x00000018   Code   RO           81    .text.DL_SYSCTL_setULPCLKDivider  ti_msp_dl_config.o
    0x000015c0   0x000015c0   0x00000028   Code   RO         1705    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x000015e8   0x000015e8   0x0000000c   Code   RO          125    .text.DL_SYSTICK_enable  ti_msp_dl_config.o
    0x000015f4   0x000015f4   0x00000028   Code   RO          123    .text.DL_SYSTICK_init  ti_msp_dl_config.o
    0x0000161c   0x0000161c   0x000000ea   Code   RO          368    .text.DL_TimerA_initPWMMode  dl_timer.o
    0x00001706   0x00001706   0x00000002   PAD
    0x00001708   0x00001708   0x00000014   Code   RO           85    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x0000171c   0x0000171c   0x0000001c   Code   RO           89    .text.DL_Timer_enableInterrupt  ti_msp_dl_config.o
    0x00001738   0x00001738   0x00000018   Code   RO           47    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x00001750   0x00001750   0x00000012   Code   RO         1574    .text.DL_Timer_getPendingInterrupt  control.o
    0x00001762   0x00001762   0x00000002   PAD
    0x00001764   0x00001764   0x0000010c   Code   RO          328    .text.DL_Timer_initPWMMode  dl_timer.o
    0x00001870   0x00001870   0x000000fc   Code   RO          298    .text.DL_Timer_initTimerMode  dl_timer.o
    0x0000196c   0x0000196c   0x00000010   Code   RO           39    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x0000197c   0x0000197c   0x00000014   Code   RO           87    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x00001990   0x00001990   0x00000030   Code   RO          342    .text.DL_Timer_setCaptCompUpdateMethod  dl_timer.o
    0x000019c0   0x000019c0   0x00000034   Code   RO          330    .text.DL_Timer_setCaptureCompareAction  dl_timer.o
    0x000019f4   0x000019f4   0x0000003c   Code   RO          306    .text.DL_Timer_setCaptureCompareCtl  dl_timer.o
    0x00001a30   0x00001a30   0x00000034   Code   RO          312    .text.DL_Timer_setCaptureCompareInput  dl_timer.o
    0x00001a64   0x00001a64   0x00000040   Code   RO          332    .text.DL_Timer_setCaptureCompareOutCtl  dl_timer.o
    0x00001aa4   0x00001aa4   0x0000002c   Code   RO          304    .text.DL_Timer_setCaptureCompareValue  dl_timer.o
    0x00001ad0   0x00001ad0   0x0000002c   Code   RO          294    .text.DL_Timer_setClockConfig  dl_timer.o
    0x00001afc   0x00001afc   0x00000020   Code   RO          302    .text.DL_Timer_setCounterValueAfterEnable  dl_timer.o
    0x00001b1c   0x00001b1c   0x00000018   Code   RO          300    .text.DL_Timer_setLoadValue  dl_timer.o
    0x00001b34   0x00001b34   0x00000016   Code   RO          224    .text.DL_UART_disable  dl_uart.o
    0x00001b4a   0x00001b4a   0x00000016   Code   RO          103    .text.DL_UART_enable  ti_msp_dl_config.o
    0x00001b60   0x00001b60   0x00000018   Code   RO          107    .text.DL_UART_enableDMAReceiveEvent  ti_msp_dl_config.o
    0x00001b78   0x00001b78   0x00000018   Code   RO           95    .text.DL_UART_enableFIFOs  ti_msp_dl_config.o
    0x00001b90   0x00001b90   0x0000001c   Code   RO          105    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x00001bac   0x00001bac   0x00000016   Code   RO          101    .text.DL_UART_enableLoopbackMode  ti_msp_dl_config.o
    0x00001bc2   0x00001bc2   0x00000002   PAD
    0x00001bc4   0x00001bc4   0x00000018   Code   RO           49    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x00001bdc   0x00001bdc   0x00000012   Code   RO         1668    .text.DL_UART_getPendingInterrupt  uart_callback.o
    0x00001bee   0x00001bee   0x00000002   PAD
    0x00001bf0   0x00001bf0   0x00000048   Code   RO          222    .text.DL_UART_init  dl_uart.o
    0x00001c38   0x00001c38   0x00000014   Code   RO          183    .text.DL_UART_isBusy  board.o
    0x00001c4c   0x00001c4c   0x00000018   Code   RO          250    .text.DL_UART_isTXFIFOFull  dl_uart.o
    0x00001c64   0x00001c64   0x00000014   Code   RO         1670    .text.DL_UART_receiveData  uart_callback.o
    0x00001c78   0x00001c78   0x00000018   Code   RO           41    .text.DL_UART_reset  ti_msp_dl_config.o
    0x00001c90   0x00001c90   0x0000004c   Code   RO           93    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x00001cdc   0x00001cdc   0x00000024   Code   RO          228    .text.DL_UART_setClockConfig  dl_uart.o
    0x00001d00   0x00001d00   0x0000001e   Code   RO           91    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x00001d1e   0x00001d1e   0x00000002   PAD
    0x00001d20   0x00001d20   0x00000024   Code   RO           97    .text.DL_UART_setRXFIFOThreshold  ti_msp_dl_config.o
    0x00001d44   0x00001d44   0x00000028   Code   RO           99    .text.DL_UART_setTXFIFOThreshold  ti_msp_dl_config.o
    0x00001d6c   0x00001d6c   0x00000016   Code   RO          252    .text.DL_UART_transmitData  dl_uart.o
    0x00001d82   0x00001d82   0x00000028   Code   RO          248    .text.DL_UART_transmitDataBlocking  dl_uart.o
    0x00001daa   0x00001daa   0x00000002   PAD
    0x00001dac   0x00001dac   0x00000120   Code   RO         1434    .text.GROUP1_IRQHandler  encoder.o
    0x00001ecc   0x00001ecc   0x00000078   Code   RO         1580    .text.Get_Target_Encoder  control.o
    0x00001f44   0x00001f44   0x0000009c   Code   RO         1578    .text.Get_Velocity_From_Encoder  control.o
    0x00001fe0   0x00001fe0   0x00000064   Code   RO         1453    .text.Get_battery_volt  adc.o
    0x00002044   0x00002044   0x000004a4   Code   RO         1471    .text.IRDM_line_inspection  ir_module.o
    0x000024e8   0x000024e8   0x000000a8   Code   RO         1582    .text.Incremental_PI_Left  control.o
    0x00002590   0x00002590   0x000000b8   Code   RO         1584    .text.Incremental_PI_Right  control.o
    0x00002648   0x00002648   0x00000084   Code   RO         1576    .text.Key           control.o
    0x000026cc   0x000026cc   0x00000052   Code   RO         1561    .text.LCD_Address_Set  lcd_init.o
    0x0000271e   0x0000271e   0x00000002   PAD
    0x00002720   0x00002720   0x00000244   Code   RO         1529    .text.LCD_Display_Status  lcd.o
    0x00002964   0x00002964   0x00000128   Code   RO         1501    .text.LCD_DrawLine  lcd.o
    0x00002a8c   0x00002a8c   0x0000002c   Code   RO         1499    .text.LCD_DrawPoint  lcd.o
    0x00002ab8   0x00002ab8   0x0000008a   Code   RO         1503    .text.LCD_DrawRectangle  lcd.o
    0x00002b42   0x00002b42   0x00000084   Code   RO         1497    .text.LCD_Fill      lcd.o
    0x00002bc6   0x00002bc6   0x00000002   PAD
    0x00002bc8   0x00002bc8   0x0000002c   Code   RO         1547    .text.LCD_GPIO_Init  lcd_init.o
    0x00002bf4   0x00002bf4   0x000001c0   Code   RO         1563    .text.LCD_Init      lcd_init.o
    0x00002db4   0x00002db4   0x00000214   Code   RO         1517    .text.LCD_ShowChar  lcd.o
    0x00002fc8   0x00002fc8   0x00000118   Code   RO         1523    .text.LCD_ShowIntNum  lcd.o
    0x000030e0   0x000030e0   0x0000007a   Code   RO         1519    .text.LCD_ShowString  lcd.o
    0x0000315a   0x0000315a   0x0000001e   Code   RO         1557    .text.LCD_WR_DATA   lcd_init.o
    0x00003178   0x00003178   0x00000014   Code   RO         1555    .text.LCD_WR_DATA8  lcd_init.o
    0x0000318c   0x0000318c   0x0000002c   Code   RO         1559    .text.LCD_WR_REG    lcd_init.o
    0x000031b8   0x000031b8   0x00000074   Code   RO         1551    .text.LCD_Writ_Bus  lcd_init.o
    0x0000322c   0x0000322c   0x00000044   Code   RO         1411    .text.LED_Flash     led.o
    0x00003270   0x00003270   0x00000010   Code   RO         1399    .text.LED_ON        led.o
    0x00003280   0x00003280   0x00000010   Code   RO         1407    .text.LED_Toggle    led.o
    0x00003290   0x00003290   0x0000005c   Code   RO         1337    .text.OLED_Clear    oled.o
    0x000032ec   0x000032ec   0x0000008c   Code   RO         1339    .text.OLED_DrawPoint  oled.o
    0x00003378   0x00003378   0x000000e4   Code   RO         1349    .text.OLED_Init     oled.o
    0x0000345c   0x0000345c   0x00000010   Code   RO         1351    .text.OLED_RST_Clr  oled.o
    0x0000346c   0x0000346c   0x00000010   Code   RO         1353    .text.OLED_RST_Set  oled.o
    0x0000347c   0x0000347c   0x00000010   Code   RO         1323    .text.OLED_RS_Clr   oled.o
    0x0000348c   0x0000348c   0x00000014   Code   RO         1321    .text.OLED_RS_Set   oled.o
    0x000034a0   0x000034a0   0x0000007c   Code   RO         1317    .text.OLED_Refresh_Gram  oled.o
    0x0000351c   0x0000351c   0x00000010   Code   RO         1325    .text.OLED_SCLK_Clr  oled.o
    0x0000352c   0x0000352c   0x00000010   Code   RO         1331    .text.OLED_SCLK_Set  oled.o
    0x0000353c   0x0000353c   0x00000010   Code   RO         1329    .text.OLED_SDIN_Clr  oled.o
    0x0000354c   0x0000354c   0x00000014   Code   RO         1327    .text.OLED_SDIN_Set  oled.o
    0x00003560   0x00003560   0x00000114   Code   RO         1341    .text.OLED_ShowChar  oled.o
    0x00003674   0x00003674   0x000000dc   Code   RO         1345    .text.OLED_ShowNumber  oled.o
    0x00003750   0x00003750   0x0000007a   Code   RO         1347    .text.OLED_ShowString  oled.o
    0x000037ca   0x000037ca   0x0000006e   Code   RO         1319    .text.OLED_WR_Byte  oled.o
    0x00003838   0x00003838   0x00000068   Code   RO           27    .text.SYSCFG_DL_ADC12_VOLTAGE_init  ti_msp_dl_config.o
    0x000038a0   0x000038a0   0x00000018   Code   RO          121    .text.SYSCFG_DL_DMA_CH0_init  ti_msp_dl_config.o
    0x000038b8   0x000038b8   0x00000008   Code   RO           29    .text.SYSCFG_DL_DMA_init  ti_msp_dl_config.o
    0x000038c0   0x000038c0   0x0000012c   Code   RO           15    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x000039ec   0x000039ec   0x0000007c   Code   RO           19    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00003a68   0x00003a68   0x00000048   Code   RO           17    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00003ab0   0x00003ab0   0x00000010   Code   RO           31    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00003ac0   0x00003ac0   0x00000030   Code   RO           21    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00003af0   0x00003af0   0x00000058   Code   RO           23    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00003b48   0x00003b48   0x0000005c   Code   RO           25    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x00003ba4   0x00003ba4   0x00000038   Code   RO           11    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00003bdc   0x00003bdc   0x00000088   Code   RO           13    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00003c64   0x00003c64   0x00000168   Code   RO         1421    .text.Set_PWM       motor.o
    0x00003dcc   0x00003dcc   0x0000000c   Code   RO          189    .text.Systick_getTick  board.o
    0x00003dd8   0x00003dd8   0x0000013c   Code   RO         1572    .text.TIMG0_IRQHandler  control.o
    0x00003f14   0x00003f14   0x00000048   Code   RO         1666    .text.UART1_IRQHandler  uart_callback.o
    0x00003f5c   0x00003f5c   0x0000002c   Code   RO          150    .text.__NVIC_ClearPendingIRQ  empty.o
    0x00003f88   0x00003f88   0x0000002c   Code   RO          152    .text.__NVIC_EnableIRQ  empty.o
    0x00003fb4   0x00003fb4   0x00000084   Code   RO           83    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x00004038   0x00004038   0x00000270   Code   RO         1664    .text.bt_control    uart_callback.o
    0x000042a8   0x000042a8   0x00000030   Code   RO          191    .text.delay_ms      board.o
    0x000042d8   0x000042d8   0x00000090   Code   RO          193    .text.delay_us      board.o
    0x00004368   0x00004368   0x0000002c   Code   RO          181    .text.fputc         board.o
    0x00004394   0x00004394   0x00000020   Code   RO         1374    .text.keyValue      key.o
    0x000043b4   0x000043b4   0x00000174   Code   RO         1378    .text.key_scan      key.o
    0x00004528   0x00004528   0x00000020   Code   RO         1380    .text.lapsKeyValue  key.o
    0x00004548   0x00004548   0x00000184   Code   RO         1382    .text.laps_key_scan  key.o
    0x000046cc   0x000046cc   0x00000080   Code   RO          148    .text.main          empty.o
    0x0000474c   0x0000474c   0x00000020   Code   RO         1586    .text.myabs         control.o
    0x0000476c   0x0000476c   0x00000030   Code   RO         1521    .text.mypow         lcd.o
    0x0000479c   0x0000479c   0x00000030   Code   RO         1343    .text.oled_pow      oled.o
    0x000047cc   0x000047cc   0x00000500   Code   RO         1619    .text.oled_show     show.o
    0x00004ccc   0x00004ccc   0x00000020   Code   RO         1748    i.__0printf         mc_p.l(printfa.o)
    0x00004cec   0x00004cec   0x0000002e   Code   RO         1874    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00004d1a   0x00004d1a   0x00000002   PAD
    0x00004d1c   0x00004d1c   0x0000002c   Code   RO         1821    i.__ARM_fpclassify  m_ps.l(fpclassify.o)
    0x00004d48   0x00004d48   0x000000ac   Code   RO         1823    i.__kernel_poly     m_ps.l(poly.o)
    0x00004df4   0x00004df4   0x00000014   Code   RO         1807    i.__mathlib_dbl_divzero  m_ps.l(dunder.o)
    0x00004e08   0x00004e08   0x00000008   Code   RO         1809    i.__mathlib_dbl_infnan2  m_ps.l(dunder.o)
    0x00004e10   0x00004e10   0x00000010   Code   RO         1810    i.__mathlib_dbl_invalid  m_ps.l(dunder.o)
    0x00004e20   0x00004e20   0x00000014   Code   RO         1811    i.__mathlib_dbl_overflow  m_ps.l(dunder.o)
    0x00004e34   0x00004e34   0x00000014   Code   RO         1813    i.__mathlib_dbl_underflow  m_ps.l(dunder.o)
    0x00004e48   0x00004e48   0x0000000e   Code   RO         1888    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00004e56   0x00004e56   0x00000002   PAD
    0x00004e58   0x00004e58   0x00000002   Code   RO         1889    i.__scatterload_null  mc_p.l(handlers.o)
    0x00004e5a   0x00004e5a   0x00000006   PAD
    0x00004e60   0x00004e60   0x0000000e   Code   RO         1890    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x00004e6e   0x00004e6e   0x00000002   PAD
    0x00004e70   0x00004e70   0x0000000c   Code   RO         1864    i.__set_errno       mc_p.l(errno.o)
    0x00004e7c   0x00004e7c   0x00000174   Code   RO         1755    i._fp_digits        mc_p.l(printfa.o)
    0x00004ff0   0x00004ff0   0x000006ec   Code   RO         1756    i._printf_core      mc_p.l(printfa.o)
    0x000056dc   0x000056dc   0x00000020   Code   RO         1757    i._printf_post_padding  mc_p.l(printfa.o)
    0x000056fc   0x000056fc   0x0000002c   Code   RO         1758    i._printf_pre_padding  mc_p.l(printfa.o)
    0x00005728   0x00005728   0x000009d4   Code   RO         1729    i.pow               m_ps.l(pow.o)
    0x000060fc   0x000060fc   0x00000048   Code   RO         1827    i.sqrt              m_ps.l(sqrt.o)
    0x00006144   0x00006144   0x00000004   PAD
    0x00006148   0x00006148   0x00000088   Data   RO         1730    .constdata          m_ps.l(pow.o)
    0x000061d0   0x000061d0   0x00000008   Data   RO         1825    .constdata          m_ps.l(qnan.o)
    0x000061d8   0x000061d8   0x00000474   Data   RO         1531    .rodata.ascii_1206  lcd.o
    0x0000664c   0x0000664c   0x000005f0   Data   RO         1532    .rodata.ascii_1608  lcd.o
    0x00006c3c   0x00006c3c   0x000011d0   Data   RO         1533    .rodata.ascii_2412  lcd.o
    0x00007e0c   0x00007e0c   0x000017c0   Data   RO         1534    .rodata.ascii_3216  lcd.o
    0x000095cc   0x000095cc   0x00000008   Data   RO          139    .rodata.gADC12_VOLTAGEClockConfig  ti_msp_dl_config.o
    0x000095d4   0x000095d4   0x00000018   Data   RO          140    .rodata.gDMA_CH0Config  ti_msp_dl_config.o
    0x000095ec   0x000095ec   0x00000003   Data   RO          131    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x000095ef   0x000095ef   0x00000001   PAD
    0x000095f0   0x000095f0   0x00000008   Data   RO          132    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x000095f8   0x000095f8   0x00000028   Data   RO          130    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00009620   0x00009620   0x00000003   Data   RO          133    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x00009623   0x00009623   0x00000001   PAD
    0x00009624   0x00009624   0x00000014   Data   RO          134    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00009638   0x00009638   0x00000002   Data   RO          135    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x0000963a   0x0000963a   0x0000000a   Data   RO          136    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x00009644   0x00009644   0x00000002   Data   RO          137    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x00009646   0x00009646   0x0000000a   Data   RO          138    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x00009650   0x00009650   0x00000474   Data   RO         1363    .rodata.oled_asc2_1206  oled.o
    0x00009ac4   0x00009ac4   0x000005f0   Data   RO         1364    .rodata.oled_asc2_1608  oled.o
    0x0000a0b4   0x0000a0b4   0x00000063   Data   RO         1539    .rodata.str1.1      lcd.o
    0x0000a117   0x0000a117   0x00000036   Data   RO         1598    .rodata.str1.1      control.o
    0x0000a14d   0x0000a14d   0x00000087   Data   RO         1625    .rodata.str1.1      show.o
    0x0000a1d4   0x0000a1d4   0x00000020   Data   RO         1887    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x0000a1f8, Size: 0x000016b0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x0000a1f8   0x00000004   Data   RW         1736    .data               mc_p.l(stdout.o)
    0x20200004   0x0000a1fc   0x00000004   Data   RW         1865    .data               mc_p.l(errno.o)
    0x20200008   0x0000a200   0x00000001   Data   RW          154    .data.Car_Mode      empty.o
    0x20200009   0x0000a201   0x00000001   Data   RW         1595    .data.Flag_Stop     control.o
    0x2020000a   0x0000a202   0x00000002   PAD
    0x2020000c   0x0000a204   0x00000004   Data   RW          155    .data.RC_Velocity   empty.o
    0x20200010   0x0000a208   0x00000004   Data   RW         1594    .data.Run_Mode      control.o
    0x20200014   0x0000a20c   0x00000004   Data   RW         1475    .data.Turn90Angle   ir_module.o
    0x20200018   0x0000a210   0x00000004   Data   RW         1593    .data.Velocity_KI   control.o
    0x2020001c   0x0000a214   0x00000004   Data   RW         1592    .data.Velocity_KP   control.o
    0x20200020   0x0000a218   0x00000004   Data   RW         1476    .data.maxTurnAngle  ir_module.o
    0x20200024   0x0000a21c   0x00000004   Data   RW         1477    .data.midTurnAngle  ir_module.o
    0x20200028   0x0000a220   0x00000004   Data   RW         1478    .data.minTurnAngle  ir_module.o
    0x2020002c   0x0000a224   0x00000004   Data   RW         1479    .data.sharpTurnAngle  ir_module.o
    0x20200030   0x0000a228   0x00000001   Data   RW          157    .data.target_laps   empty.o
    0x20200031   0x0000a229   0x00000007   PAD
    0x20200038        -       0x00000001   Zero   RW         1626    .bss.APP_Show.flag  show.o
    0x20200039        -       0x00000001   Zero   RW         1678    .bss.BTBufferHandler.handleSize  uart_callback.o
    0x2020003a        -       0x00000001   Zero   RW         1677    .bss.BTBufferHandler.handleflag  uart_callback.o
    0x2020003b        -       0x00000001   Zero   RW         1679    .bss.BTBufferHandler.lastSize  uart_callback.o
    0x2020003c        -       0x00000004   Zero   RW         1444    .bss.Get_Encoder_countA  encoder.o
    0x20200040        -       0x00000004   Zero   RW         1445    .bss.Get_Encoder_countB  encoder.o
    0x20200044        -       0x00000004   Zero   RW         1485    .bss.IRDM_line_inspection.corner_detect_delay  ir_module.o
    0x20200048        -       0x00000004   Zero   RW         1480    .bss.IRDM_line_inspection.last_state  ir_module.o
    0x2020004c        -       0x00000004   Zero   RW         1481    .bss.IRDM_line_inspection.ten_time  ir_module.o
    0x20200050        -       0x00000004   Zero   RW         1484    .bss.IRDM_line_inspection.turn_cooldown  ir_module.o
    0x20200054        -       0x00000004   Zero   RW         1483    .bss.IRDM_line_inspection.turn_counter  ir_module.o
    0x20200058        -       0x00000004   Zero   RW         1482    .bss.IRDM_line_inspection.turn_phase  ir_module.o
    0x2020005c        -       0x00000004   Zero   RW         1600    .bss.Incremental_PI_Left.Bias  control.o
    0x20200060        -       0x00000004   Zero   RW         1602    .bss.Incremental_PI_Left.Last_bias  control.o
    0x20200064        -       0x00000004   Zero   RW         1601    .bss.Incremental_PI_Left.Pwm  control.o
    0x20200068        -       0x00000004   Zero   RW         1603    .bss.Incremental_PI_Right.Bias  control.o
    0x2020006c        -       0x00000004   Zero   RW         1605    .bss.Incremental_PI_Right.Last_bias  control.o
    0x20200070        -       0x00000004   Zero   RW         1604    .bss.Incremental_PI_Right.Pwm  control.o
    0x20200074        -       0x00000002   Zero   RW         1413    .bss.LED_Flash.temp  led.o
    0x20200076   0x0000a229   0x00000002   PAD
    0x20200078        -       0x00000010   Zero   RW         1596    .bss.MotorA         control.o
    0x20200088        -       0x00000010   Zero   RW         1597    .bss.MotorB         control.o
    0x20200098        -       0x00000004   Zero   RW          165    .bss.Move_X         empty.o
    0x2020009c        -       0x00000004   Zero   RW          167    .bss.Move_Z         empty.o
    0x202000a0        -       0x00000400   Zero   RW         1366    .bss.OLED_GRAM      oled.o
    0x202004a0        -       0x00000008   Zero   RW         1599    .bss.OriginalEncoder  control.o
    0x202004a8        -       0x00000001   Zero   RW          163    .bss.PID_Send       empty.o
    0x202004a9   0x0000a229   0x00000003   PAD
    0x202004ac        -       0x00000004   Zero   RW         1686    .bss.Turn_Flag      uart_callback.o
    0x202004b0        -       0x00000004   Zero   RW          169    .bss.Velocity_Left  empty.o
    0x202004b4        -       0x00000004   Zero   RW          170    .bss.Velocity_Right  empty.o
    0x202004b8        -       0x00000004   Zero   RW          156    .bss.Voltage        empty.o
    0x202004bc        -       0x00000004   Zero   RW         1685    .bss.bt_control.Data  uart_callback.o
    0x202004c0        -       0x00000001   Zero   RW         1681    .bss.bt_control.Flag_PID  uart_callback.o
    0x202004c1        -       0x00000032   Zero   RW         1684    .bss.bt_control.Receive  uart_callback.o
    0x202004f3   0x0000a229   0x00000001   PAD
    0x202004f4        -       0x00000004   Zero   RW         1680    .bss.bt_control.Usart_Receive  uart_callback.o
    0x202004f8        -       0x00000001   Zero   RW         1682    .bss.bt_control.i   uart_callback.o
    0x202004f9        -       0x00000001   Zero   RW         1683    .bss.bt_control.j   uart_callback.o
    0x202004fa        -       0x00000001   Zero   RW          159    .bss.corner_count   empty.o
    0x202004fb        -       0x00000001   Zero   RW          158    .bss.current_laps   empty.o
    0x202004fc        -       0x00000001   Zero   RW         1673    .bss.gBTCounts      uart_callback.o
    0x202004fd        -       0x000000c8   Zero   RW         1676    .bss.gBTPacket      uart_callback.o
    0x202005c5        -       0x00000001   Zero   RW         1463    .bss.gCheckADC      adc.o
    0x202005c6   0x0000a229   0x00000002   PAD
    0x202005c8        -       0x000000bc   Zero   RW          129    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x20200684        -       0x00000004   Zero   RW         1442    .bss.gpio_interrup1  encoder.o
    0x20200688        -       0x00000004   Zero   RW         1443    .bss.gpio_interrup2  encoder.o
    0x2020068c        -       0x00000004   Zero   RW         1489    .bss.ir_dh1_state   ir_module.o
    0x20200690        -       0x00000004   Zero   RW         1488    .bss.ir_dh2_state   ir_module.o
    0x20200694        -       0x00000004   Zero   RW         1487    .bss.ir_dh3_state   ir_module.o
    0x20200698        -       0x00000004   Zero   RW         1486    .bss.ir_dh4_state   ir_module.o
    0x2020069c        -       0x00000001   Zero   RW         1387    .bss.key_scan.check_once  key.o
    0x2020069d   0x0000a229   0x00000001   PAD
    0x2020069e        -       0x00000002   Zero   RW         1385    .bss.key_scan.long_press_time  key.o
    0x202006a0        -       0x00000001   Zero   RW         1386    .bss.key_scan.press_flag  key.o
    0x202006a1   0x0000a229   0x00000001   PAD
    0x202006a2        -       0x00000002   Zero   RW         1384    .bss.key_scan.time_core  key.o
    0x202006a4        -       0x00000001   Zero   RW         1391    .bss.laps_key_scan.check_once  key.o
    0x202006a5   0x0000a229   0x00000001   PAD
    0x202006a6        -       0x00000002   Zero   RW         1389    .bss.laps_key_scan.long_press_time  key.o
    0x202006a8        -       0x00000001   Zero   RW         1390    .bss.laps_key_scan.press_flag  key.o
    0x202006a9   0x0000a229   0x00000001   PAD
    0x202006aa        -       0x00000002   Zero   RW         1388    .bss.laps_key_scan.time_core  key.o
    0x202006ac   0x0000a229   0x00000004   PAD
    0x202006b0        -       0x00001000   Zero   RW            1    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       230         40          0          0          1       4513   adc.o
       268         24          0          0          0       4700   board.o
      1126        128         54         13         64       6540   control.o
       112          8          0          0          0       4110   dl_adc12.o
        20          0          0          0          0        655   dl_common.o
       250          4          0          0          0       4223   dl_dma.o
      1214        180          0          0          0      20693   dl_timer.o
       256         12          0          0          0      10702   dl_uart.o
       216         12          0          6         23       3025   empty.o
       362         32          0          0         16       4192   encoder.o
      1210        184          0         20         40       4799   ir_module.o
       846         56          0          0         12       4879   key.o
      2172         76      13399          0          0      10163   lcd.o
       828          8          0          0          0       4735   lcd_init.o
       144          8          0          0          2       4171   led.o
       400          8          0          0          0       5942   motor.o
      1536         20       2660          0       1024       7809   oled.o
      1700        232        135          0          1       2362   show.o
        20          4        192          0       4096        708   startup_mspm0g350x_uvision.o
      2450        240        130          0        188      38107   ti_msp_dl_config.o
      1246        168          0          0        269       7062   uart_callback.o

    ----------------------------------------------------------------------
     16628       <USER>      <GROUP>         44       5756     154090   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          2          5         20          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       232         20          0          0          0      18490   dl_sysctl_mspm0g1x0x_g3x0x.o
        84         14          0          0          0        340   dunder.o
        44          4          0          0          0         60   fpclassify.o
       172          0          0          0          0         76   poly.o
      2516        206        136          0          0        236   pow.o
         0          0          8          0          0          0   qnan.o
        72          6          0          0          0         76   sqrt.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         60   errno.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0        100   memseta.o
      2252         98          0          0          0        412   printfa.o
         0          0          0          4          0          0   stdout.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
        56          0          0          0          0         68   d2f.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        72         10          0          0          0         72   dfixi.o
        64         10          0          0          0         68   dfixul.o
        40          6          0          0          0         68   dflti.o
       208          6          0          0          0         88   dmul.o
        44          0          0          0          0         72   dscalb.o
       162          0          0          0          0         80   dsqrt.o
        40          0          0          0          0         60   f2d.o
       178          0          0          0          0        108   fadd.o
        28          0          0          0          0         60   fcmpge.o
        28          0          0          0          0         60   fcmple.o
        28          0          0          0          0         60   fcmplt.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        50          0          0          0          0         60   ffixi.o
        22          0          0          0          0         68   fflti.o
        14          0          0          0          0         68   ffltui.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      8084        <USER>        <GROUP>          8          0      22134   Library Totals
        22          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       232         20          0          0          0      18490   driverlib.a
      2888        230        144          0          0        788   m_ps.l
      2660        122          0          8          0       1000   mc_p.l
      2282         44          0          0          0       1856   mf_p.l

    ----------------------------------------------------------------------
      8084        <USER>        <GROUP>          8          0      22134   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     24712       1864      16748         52       5756     174120   Grand Totals
     24712       1864      16748         52       5756     174120   ELF Image Totals
     24712       1864      16748         52          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                41460 (  40.49kB)
    Total RW  Size (RW Data + ZI Data)              5808 (   5.67kB)
    Total ROM Size (Code + RO Data + RW Data)      41512 (  40.54kB)

==============================================================================

