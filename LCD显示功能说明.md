# LCD显示功能说明

## 功能概述

已将原有的串口和蓝牙输出替换为LCD屏幕显示，实现了英文界面的状态显示功能。

## 显示内容

### 主界面显示信息
1. **标题**: "Line Following Car"
2. **目标圈数**: "Target Laps: XX"
3. **当前完成圈数**: "Current Laps: XX"  
4. **直角弯计数**: "Corner Count: X/4"
5. **运行状态**: "Status: RUNNING/STOPPED"
6. **进度条**: 可视化显示完成进度
7. **完成百分比**: "XX%"

### 颜色方案
- 背景色：黑色
- 文字：白色
- 运行状态：绿色(RUNNING) / 红色(STOPPED)
- 进度条：绿色填充，白色边框

## 硬件连接

### LCD GPIO引脚定义
- SCL (时钟线): PB0
- SDA (数据线): PB1  
- RES (复位线): PB3
- DC (数据/命令选择): PB4
- CS (片选线): PB5
- BLK (背光控制): PB8

### LCD规格
- 分辨率: 320x240 (横屏模式)
- 接口: SPI
- 颜色: 16位RGB565

## 功能触发

### 自动更新时机
1. **按下PB13按键**: 目标圈数增加，LCD立即更新显示
2. **按下PA18按键**: 启动/停止状态改变，LCD更新显示
3. **检测到直角弯**: 直角弯计数增加，LCD更新显示
4. **完成一圈**: 当前圈数增加，LCD更新显示
5. **达到目标圈数**: 停车状态，LCD更新显示

### 显示更新函数
```c
void LCD_Display_Status(void); // 更新LCD显示状态
```

## 代码修改说明

### 1. 头文件添加
在`Hardware/board.h`中添加：
- `#include "lcd.h"`
- `#include "lcd_init.h"`
- LCD GPIO引脚定义

### 2. 初始化修改
在`empty.c`中添加：
```c
LCD_Init();           // 初始化LCD显示屏
LCD_Display_Status(); // 显示初始状态
```

### 3. 按键处理修改
在`Control/control.c`中：
- 移除`printf`串口输出
- 添加`LCD_Display_Status()`调用

### 4. 状态检测修改
在`Hardware/IR_Module.c`中：
- 移除`printf`串口输出
- 添加`LCD_Display_Status()`调用

## 使用方法

### 1. 设置目标圈数
- 按下PB13按键，LCD显示的"Target Laps"数值会增加
- 范围：1-10圈，超过10圈会回到1圈

### 2. 启动循迹
- 按下PA18按键启动小车
- LCD显示状态变为"RUNNING"
- 当前圈数和直角弯计数重置为0

### 3. 监控进度
- 实时查看LCD屏幕上的进度信息
- 进度条可视化显示完成百分比
- 直角弯计数显示当前圈的进度

### 4. 完成停车
- 达到目标圈数后自动停车
- LCD显示状态变为"STOPPED"
- 计数器自动重置

## 优势特点

1. **直观显示**: 图形化界面比串口输出更直观
2. **实时更新**: 状态变化立即反映在屏幕上
3. **英文界面**: 便于国际化使用
4. **进度可视化**: 进度条和百分比显示更清晰
5. **无需外部设备**: 不依赖串口调试工具

## 注意事项

1. **GPIO配置**: 确保LCD相关GPIO引脚正确配置
2. **SPI通信**: LCD使用模拟SPI通信，注意时序
3. **背光控制**: BLK引脚控制背光开关
4. **屏幕方向**: 当前配置为横屏显示(320x240)
5. **刷新频率**: 避免过于频繁的屏幕刷新

## 故障排除

### 屏幕无显示
- 检查GPIO连接是否正确
- 确认背光是否开启(BLK引脚)
- 检查电源供电是否正常

### 显示内容错误
- 确认变量值是否正确更新
- 检查LCD_Display_Status()函数调用
- 验证字符串和数值显示函数

### 显示卡顿
- 减少不必要的屏幕刷新
- 优化显示更新逻辑
- 检查SPI通信时序

## 版本信息

- 功能版本: v1.0
- 适用平台: WHEELTEC C07A + MSPM0G3507
- LCD型号: 通用SPI接口LCD (320x240)
- 更新时间: 2024年
