/***********************************************
????????????????????????
????WHEELTEC
??????wheeltec.net
????????shop114407458.taobao.com
?????: https://minibalance.aliexpress.com/store/4455017
?��??5.7
??????2021-04-29


Brand: WHEELTEC
Website: wheeltec.net
Taobao shop: shop114407458.taobao.com
Aliexpress: https://minibalance.aliexpress.com/store/4455017
Version: 5.7
Update??2021-04-29

All rights reserved
***********************************************/
#include "control.h"

u8 CCD_count,ELE_count;
int Sensor_Left,Sensor_Middle,Sensor_Right,Sensor;

Encoder OriginalEncoder; 					//????????????   
Motor_parameter MotorA,MotorB;				//????????????
float Velocity_KP=3400,Velocity_KI=1450;
int Run_Mode=1;//小车运行模式 - 固定为寻迹模式
u8 Flag_Stop=1;
void TIMER_0_INST_IRQHandler(void)
{
    if(DL_TimerA_getPendingInterrupt(TIMER_0_INST))
    {
        if(DL_TIMER_IIDX_ZERO)
        {
			
			Key();
			LED_Flash(100);
			Get_Velocity_From_Encoder(Get_Encoder_countA,Get_Encoder_countB);
			Get_Encoder_countA=Get_Encoder_countB=0;
			if(!Flag_Stop)
			{
				// 运行状态：执行寻迹和PI控制
				IRDM_line_inspection();
				Get_Target_Encoder(Move_X,Move_Z);
				MotorA.Motor_Pwm = Incremental_PI_Left(MotorA.Current_Encoder,MotorA.Target_Encoder);
				MotorB.Motor_Pwm = Incremental_PI_Right(MotorB.Current_Encoder,MotorB.Target_Encoder);
				printf("运行: PwmA=%.1f, PwmB=%.1f\r\n", MotorA.Motor_Pwm, MotorB.Motor_Pwm);
				Set_PWM(MotorA.Motor_Pwm,MotorB.Motor_Pwm);
			}else {
				// 停止状态：清零所有控制量，重置PI控制器
				Move_X = 0;
				Move_Z = 0;
				MotorA.Target_Encoder = 0;
				MotorB.Target_Encoder = 0;
				MotorA.Current_Encoder = 0;
				MotorB.Current_Encoder = 0;
				// 通过调用PI函数并传入0来重置积分项
				MotorA.Motor_Pwm = Incremental_PI_Left(0, 0);
				MotorB.Motor_Pwm = Incremental_PI_Right(0, 0);
				// 强制清零PWM输出
				MotorA.Motor_Pwm = 0;
				MotorB.Motor_Pwm = 0;
				printf("停止: PWM已清零\r\n");
				Set_PWM(0,0);
			}
		}
    }
}

/**************************************************************************
Function: Get_Velocity_From_Encoder
Input   : none
Output  : none
????????????????????????????
??????: ?? 
????  ?????
**************************************************************************/	 	
void Get_Velocity_From_Encoder(int Encoder1,int Encoder2)
{
	
	//Retrieves the original data of the encoder
	//?????????????????
	float Encoder_A_pr,Encoder_B_pr; 
	OriginalEncoder.A = Encoder1;   // 左编码器不取反（电机方向已改变）
	OriginalEncoder.B = Encoder2;   // 右编码器不取反（电机方向已改变）
	Encoder_A_pr=OriginalEncoder.A; Encoder_B_pr=OriginalEncoder.B;
	//??????????????????????????��m/s
	MotorA.Current_Encoder= Encoder_A_pr*Frequency*Perimeter/780.0f;  
	MotorB.Current_Encoder= Encoder_B_pr*Frequency*Perimeter/780.0f;   //1560=2*13*30=2????��???��*1?????????????*??????????13??*?????????
	
}
//?????????x??y??????????????????,Vx??m/s,Vz??��???/s(?????)
void Get_Target_Encoder(float Vx,float Vz)
{
	float amplitude=3.5f; //Wheel target speed limit //?????????????
	if(Vx<0) Vz=-Vz;
	else     Vz=Vz;
	//Inverse kinematics //???????
	 MotorA.Target_Encoder = Vx - Vz * Wheelspacing / 2.0f; //????????????????
	 MotorB.Target_Encoder = Vx + Vz * Wheelspacing / 2.0f; //????????????????
	//Wheel (motor) target speed limit //????(???)?????????
//	 MotorA.Target_Encoder=target_limit_float( MotorA.Target_Encoder,-amplitude,amplitude); 
//	 MotorB.Target_Encoder=target_limit_float( MotorB.Target_Encoder,-amplitude,amplitude); 
}


/**************************************************************************
Function: Absolute value function
Input   : a??Number to be converted
Output  : unsigned int
??????????????????
????????a?????????????????
????  ????????????
**************************************************************************/
int myabs(int a)
{
	int temp;
	if(a<0)  temp=-a;
	else temp=a;
	return temp;
}

int Turn_Off(void)
{
	u8 temp = 0;
//	if(Voltage>700&&EN==0)//???????7V?????????
//	{
//		temp = 1;
//	}
	return temp;			
}
/**************************************************************************
Function: PWM_Limit
Input   : IN;max;min
Output  : OUT
?????????????PWM???
??????: IN?????????  max?????????  min???????��? 
????  ??????????
**************************************************************************/	 	
float PWM_Limit(float IN,float max,float min)
{
	float OUT = IN;
	if(OUT>max) OUT = max;
	if(OUT<min) OUT = min;
	return OUT;
}
/**************************************************************************
?????????????PI??????
???????????????????????????
????  ??????PWM
????????????PID??? 
pwm+=Kp[e??k??-e(k-1)]+Ki*e(k)+Kd[e(k)-2e(k-1)+e(k-2)]
e(k)??????????? 
e(k-1)????????��????  ??????? 
pwm???????????
????????????????????��????PI????
pwm+=Kp[e??k??-e(k-1)]+Ki*e(k)
**************************************************************************/
int Incremental_PI_Left (float Encoder,float Target)
{ 	
	 static float Bias,Pwm,Last_bias;
	 Bias=Target-Encoder;                					//???????
	 Pwm+=Velocity_KP*(Bias-Last_bias)+Velocity_KI*Bias;   	//?????PI??????
	 if(Pwm>2000)Pwm=2000;
	 if(Pwm<-2000)Pwm=-2000;
	 Last_bias=Bias;	                   					//???????????? 
	 return Pwm;                         					//???????
}


int Incremental_PI_Right (float Encoder,float Target)
{ 	
	 static float Bias,Pwm,Last_bias;
	 Bias=Target-Encoder;                					//???????
	 Pwm+=Velocity_KP*(Bias-Last_bias)+Velocity_KI*Bias;   	//?????PI??????
	 if(Pwm>2000)Pwm=2000;
	 if(Pwm<-2000)Pwm=-2000;
	 Last_bias=Bias;	                   					//???????????? 
	 return Pwm;                         					//???????
}
/**************************************************************************
Function: Processes the command sent by APP through usart 2
Input   : none
Output  : none
???????????APP???????2???????????????��???
??????????
????  ?????
**************************************************************************/
// Get_RC函数已删除 - 不再需要遥控功能，只使用灰度传感器自动寻迹

/**************************************************************************
Function: Press the key to modify the car running state
Input   : none
Output  : none
????????????????��????????
??????????
????  ?????
**************************************************************************/
void Key(void)
{
	u8 tmp,tmp2;
	tmp=key_scan(200);//click_N_Double(50);
	if(tmp==1)
	{
		Flag_Stop=!Flag_Stop;
		if(Flag_Stop == 0) // 启动时重置计数器
		{
			current_laps = 0;
			corner_count = 0;
			printf("小车启动，开始%d圈巡线\r\n", target_laps);
		}
		else
		{
			printf("小车停止\r\n");
		}
	}		//按键控制小车启动停止

	// laps按键处理 - 设置目标圈数
	tmp2=laps_key_scan(200);
	if(tmp2==1) // 单击增加圈数
	{
		target_laps++;
		if(target_laps > 10) target_laps = 1; // 最多10圈，超过则回到1圈
		printf("目标圈数设置: %d圈\r\n", target_laps);
	}
	// 移除模式切换功能，固定为寻迹模式
}
