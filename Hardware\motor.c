#include "motor.h"
/***********************************************
????????????????????????
????WHEELTEC
??????wheeltec.net
????????shop114407458.taobao.com 
?????: https://minibalance.aliexpress.com/store/4455017
?????V1.0
??????2024-07-019

Brand: WHEELTEC
Website: wheeltec.net
Taobao shop: shop114407458.taobao.com 
Aliexpress: https://minibalance.aliexpress.com/store/4455017
Version: V1.0
Update??2024-07-019

All rights reserved
***********************************************/
void Set_PWM(int pwmL,int pwmR)
{
	// ????????
	if(pwmL == 0)
	{
		// ?????????
		DL_GPIO_clearPins(AIN_PORT,AIN_AIN1_PIN);
		DL_GPIO_clearPins(AIN_PORT,AIN_AIN2_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,0,GPIO_PWM_0_C0_IDX);
	}
	else if(pwmL>0)
    {
        DL_GPIO_setPins(AIN_PORT,AIN_AIN1_PIN);  // ��������PWM��ǰת
        DL_GPIO_clearPins(AIN_PORT,AIN_AIN2_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmL),GPIO_PWM_0_C0_IDX);
    }
    else
    {
        DL_GPIO_setPins(AIN_PORT,AIN_AIN2_PIN);  // ��������PWM���ת
        DL_GPIO_clearPins(AIN_PORT,AIN_AIN1_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmL),GPIO_PWM_0_C0_IDX);
    }

    // ????????
    if(pwmR == 0)
	{
		// ?????????
		DL_GPIO_clearPins(BIN_PORT,BIN_BIN1_PIN);
		DL_GPIO_clearPins(BIN_PORT,BIN_BIN2_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,0,GPIO_PWM_0_C1_IDX);
	}
	else if(pwmR>0)
    {
		DL_GPIO_setPins(BIN_PORT,BIN_BIN1_PIN);  // ��������PWM��ǰת
        DL_GPIO_clearPins(BIN_PORT,BIN_BIN2_PIN);
        DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmR),GPIO_PWM_0_C1_IDX);
    }
    else
    {
		DL_GPIO_setPins(BIN_PORT,BIN_BIN2_PIN);  // ��������PWM���ת
        DL_GPIO_clearPins(BIN_PORT,BIN_BIN1_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmR),GPIO_PWM_0_C1_IDX);
    }
}



