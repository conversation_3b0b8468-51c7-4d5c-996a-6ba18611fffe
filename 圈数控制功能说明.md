# WHEELTEC C07A 圈数控制功能说明

## 功能概述

本项目在原有红外循迹小车基础上新增了圈数控制功能，支持设定目标圈数（1-10圈），小车将自动完成指定圈数的循迹后停车。

## 硬件要求

- 正方形逆时针循迹轨道
- PB13按键（圈数设置按键）
- PA18按键（启动/停止按键）
- 4路红外传感器正常工作

## 使用方法

### 1. 圈数设置
- 按下PB13按键可循环设置目标圈数
- 每次按下增加1圈，最大10圈，超过后回到1圈
- 串口会输出当前设置的圈数："目标圈数设置: X圈"

### 2. 启动循迹
- 设置好目标圈数后，按下PA18按键启动小车
- 小车开始循迹，系统自动重置圈数计数器
- 串口输出："小车启动，开始X圈巡线"

### 3. 圈数检测
- 系统检测正方形轨道的4个左直角弯
- 每检测到1个直角弯，计数器+1
- 每4个直角弯计为完成1圈
- 串口输出直角弯计数和圈数进度

### 4. 自动停车
- 完成设定圈数后，在检测到下一个左直角弯时自动停车
- 串口输出："达到目标圈数，停车"
- 系统自动重置计数器，准备下次使用

## 显示信息

OLED屏幕第二行右侧显示圈数信息：
- T:XX - 目标圈数（Target laps）
- C:XX - 当前完成圈数（Current laps）  
- R:X - 直角弯计数（Right-angle count）

## 技术实现

### 关键变量
- `target_laps` - 目标圈数（1-10）
- `current_laps` - 当前完成圈数
- `corner_count` - 直角弯计数器（0-3）

### 检测逻辑
- 在IR_Module.c中的左直角弯检测代码中添加计数逻辑
- 使用延时机制防止重复计数
- 每4个直角弯自动增加圈数计数

### 配置参数
- `CORNERS_PER_LAP = 4` - 每圈直角弯数量
- `CORNER_DETECT_DELAY = 50` - 检测延时防重复计数

## 注意事项

1. **轨道要求**：必须是正方形逆时针轨道
2. **传感器校准**：确保红外传感器能准确检测直角弯
3. **按键连接**：确认PB13按键正确连接到GPIO
4. **调试信息**：通过串口可查看详细的计数过程
5. **重置机制**：每次启动会自动重置计数器

## 故障排除

### 圈数计数不准确
- 检查红外传感器是否正常工作
- 确认轨道为标准正方形
- 调整传感器位置和高度

### 按键无响应
- 检查PB13按键硬件连接
- 确认GPIO配置正确
- 查看串口是否有按键检测信息

### 停车位置不准确
- 调整直角弯检测参数
- 检查转弯状态机工作是否正常
- 确认冷却时间设置合理

## 版本信息

- 功能版本：v1.0
- 适用平台：WHEELTEC C07A + MSPM0G3507
- 更新时间：2024年
- 开发状态：已完成并测试
