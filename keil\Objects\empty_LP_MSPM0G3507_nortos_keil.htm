<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\empty_LP_MSPM0G3507_nortos_keil.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\empty_LP_MSPM0G3507_nortos_keil.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6210000: Last Updated: Sat Aug  2 14:41:45 2025
<BR><P>
<H3>Maximum Stack Usage =        384 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; BTBufferHandler &rArr; bt_control &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">SVC_Handler</a><BR>
 <LI><a href="#[4]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">PendSV_Handler</a><BR>
 <LI><a href="#[5]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">SysTick_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from adc.o(.text.ADC1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1b]">AES_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[c]">CANFD0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[d]">DAC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1d]">DMA_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from encoder.o(.text.GROUP1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[19]">I2C0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1a]">I2C1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1c]">RTC_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[e]">SPI0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[f]">SPI1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[15]">TIMA0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[16]">TIMA1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[13]">TIMG0_IRQHandler</a> from control.o(.text.TIMG0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[18]">TIMG12_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[14]">TIMG6_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[17]">TIMG7_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[12]">UART0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[10]">UART1_IRQHandler</a> from uart_callback.o(.text.UART1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[11]">UART2_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[9]">UART3_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1f]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_mspm0g350x_uvision.o(.text)
 <LI><a href="#[20]">fputc</a> from board.o(.text.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[1e]">main</a> from empty.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1f]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(.text)
</UL>
<P><STRONG><a name="[f2]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[21]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[3f]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[f3]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[f4]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[f5]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[f6]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[f7]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>AES_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>Default_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>

<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>I2C0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>UART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>UART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[f8]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[f9]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[24]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
</UL>

<P><STRONG><a name="[fa]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[fb]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[26]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[27]"></a>__aeabi_fadd</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Right
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Left
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Target_Encoder
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
</UL>

<P><STRONG><a name="[2a]"></a>__aeabi_fsub</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Right
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Left
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Target_Encoder
</UL>

<P><STRONG><a name="[2b]"></a>__aeabi_frsub</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[45]"></a>__aeabi_fmul</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, fmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Right
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Left
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Target_Encoder
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Velocity_From_Encoder
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IRDM_line_inspection
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_scan
</UL>

<P><STRONG><a name="[2c]"></a>__aeabi_fdiv</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Target_Encoder
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IRDM_line_inspection
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_scan
</UL>

<P><STRONG><a name="[2d]"></a>__aeabi_dadd</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[32]"></a>__aeabi_dsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[33]"></a>__aeabi_drsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[34]"></a>__aeabi_dmul</STRONG> (Thumb, 202 bytes, Stack size 72 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_battery_volt
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Velocity_From_Encoder
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[35]"></a>__aeabi_ddiv</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_battery_volt
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Velocity_From_Encoder
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[7e]"></a>__aeabi_fcmple</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Right
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Left
</UL>

<P><STRONG><a name="[e0]"></a>__aeabi_fcmplt</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmplt.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
</UL>

<P><STRONG><a name="[76]"></a>__aeabi_fcmpge</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmpge.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Right
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Left
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Target_Encoder
</UL>

<P><STRONG><a name="[36]"></a>__aeabi_i2f</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_i2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Velocity_From_Encoder
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[37]"></a>__aeabi_ui2f</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ffltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_ui2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_scan
</UL>

<P><STRONG><a name="[38]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_battery_volt
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[43]"></a>__aeabi_f2iz</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, ffixi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Right
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Left
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_scan
</UL>

<P><STRONG><a name="[39]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __aeabi_d2iz &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
</UL>

<P><STRONG><a name="[44]"></a>__aeabi_f2d</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Velocity_From_Encoder
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[3b]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_battery_volt
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Velocity_From_Encoder
</UL>

<P><STRONG><a name="[9a]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, uidiv_div0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNumber
</UL>

<P><STRONG><a name="[9b]"></a>__aeabi_uidivmod</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, uidiv_div0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNumber
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[3c]"></a>__aeabi_uldivmod</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[2e]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[fc]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[3a]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[fd]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[2f]"></a>__aeabi_lasr</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, llsshr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_lasr
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[fe]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[ff]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[29]"></a>_float_round</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>

<P><STRONG><a name="[28]"></a>_float_epilogue</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
</UL>

<P><STRONG><a name="[31]"></a>_double_round</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[30]"></a>_double_epilogue</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_clz
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>

<P><STRONG><a name="[e9]"></a>__ARM_scalbn</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[100]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[3e]"></a>__aeabi_d2ulz</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[ec]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[22]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[101]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[40]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, adc.o(.text.ADC1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ADC1_IRQHandler &rArr; DL_ADC12_getPendingInterrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_getPendingInterrupt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>APP_Show</STRONG> (Thumb, 340 bytes, Stack size 80 bytes, show.o(.text.APP_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = APP_Show &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[47]"></a>BTBufferHandler</STRONG> (Thumb, 188 bytes, Stack size 24 bytes, uart_callback.o(.text.BTBufferHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = BTBufferHandler &rArr; bt_control &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Systick_getTick
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BT_DAMConfig
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_getTransferSize
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4b]"></a>BT_DAMConfig</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, uart_callback.o(.text.BT_DAMConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = BT_DAMConfig &rArr; DL_DMA_setTransferSize
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_enableChannel
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_setTransferSize
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_setDestAddr
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_setSrcAddr
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_disableChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BTBufferHandler
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[53]"></a>DL_ADC12_setClockConfig</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, dl_adc12.o(.text.DL_ADC12_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_ADC12_setClockConfig &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_VOLTAGE_init
</UL>

<P><STRONG><a name="[d1]"></a>DL_Common_delayCycles</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dl_common.o(.text.DL_Common_delayCycles))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Common_delayCycles
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[55]"></a>DL_DMA_initChannel</STRONG> (Thumb, 70 bytes, Stack size 56 bytes, dl_dma.o(.text.DL_DMA_initChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = DL_DMA_initChannel &rArr; DL_DMA_setTrigger &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_setTrigger
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_configTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_CH0_init
</UL>

<P><STRONG><a name="[b7]"></a>DL_SYSCTL_configSYSPLL</STRONG> (Thumb, 192 bytes, Stack size 36 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DL_SYSCTL_configSYSPLL
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[b8]"></a>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[5c]"></a>DL_TimerA_initPWMMode</STRONG> (Thumb, 234 bytes, Stack size 56 bytes, dl_timer.o(.text.DL_TimerA_initPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareAction
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareInput
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareCtl
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[61]"></a>DL_Timer_initPWMMode</STRONG> (Thumb, 260 bytes, Stack size 48 bytes, dl_timer.o(.text.DL_Timer_initPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareAction
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareInput
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareCtl
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterValueAfterEnable
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setLoadValue
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
</UL>

<P><STRONG><a name="[65]"></a>DL_Timer_initTimerMode</STRONG> (Thumb, 252 bytes, Stack size 24 bytes, dl_timer.o(.text.DL_Timer_initTimerMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = DL_Timer_initTimerMode &rArr; DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareCtl
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterValueAfterEnable
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setLoadValue
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
</UL>

<P><STRONG><a name="[67]"></a>DL_Timer_setCaptCompUpdateMethod</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_Timer_setCaptCompUpdateMethod &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[5d]"></a>DL_Timer_setCaptureCompareAction</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareAction))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_Timer_setCaptureCompareAction &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
</UL>

<P><STRONG><a name="[5e]"></a>DL_Timer_setCaptureCompareCtl</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareCtl))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
</UL>

<P><STRONG><a name="[60]"></a>DL_Timer_setCaptureCompareInput</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
</UL>

<P><STRONG><a name="[5f]"></a>DL_Timer_setCaptureCompareOutCtl</STRONG> (Thumb, 60 bytes, Stack size 36 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DL_Timer_setCaptureCompareOutCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[66]"></a>DL_Timer_setCaptureCompareValue</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Timer_setCaptureCompareValue
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_PWM
</UL>

<P><STRONG><a name="[b0]"></a>DL_Timer_setClockConfig</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[68]"></a>DL_UART_init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_UART_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_UART_init &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[c0]"></a>DL_UART_setClockConfig</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[6f]"></a>DL_UART_transmitDataBlocking</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_UART_transmitDataBlocking))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_UART_transmitDataBlocking &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitData
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_isTXFIFOFull
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 264 bytes, Stack size 16 bytes, encoder.o(.text.GROUP1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GROUP1_IRQHandler &rArr; DL_GPIO_clearInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearInterruptStatus
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_readPins
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_getEnabledInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>Get_Target_Encoder</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, control.o(.text.Get_Target_Encoder))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = Get_Target_Encoder &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[77]"></a>Get_Velocity_From_Encoder</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, control.o(.text.Get_Velocity_From_Encoder))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = Get_Velocity_From_Encoder &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[78]"></a>Get_battery_volt</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, adc.o(.text.Get_battery_volt))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = Get_battery_volt &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_getMemResult
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_startConversion
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7b]"></a>IRDM_line_inspection</STRONG> (Thumb, 968 bytes, Stack size 72 bytes, ir_module.o(.text.IRDM_line_inspection))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = IRDM_line_inspection &rArr; printf
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_readPins
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[7d]"></a>Incremental_PI_Left</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, control.o(.text.Incremental_PI_Left))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = Incremental_PI_Left &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[7f]"></a>Incremental_PI_Right</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, control.o(.text.Incremental_PI_Right))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = Incremental_PI_Right &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[80]"></a>Key</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, control.o(.text.Key))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Key &rArr; key_scan &rArr; keyValue &rArr; DL_GPIO_readPins
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_scan
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[82]"></a>LED_Flash</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, led.o(.text.LED_Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LED_Flash &rArr; LED_Toggle &rArr; DL_GPIO_togglePins
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Toggle
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_ON
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[83]"></a>LED_ON</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, led.o(.text.LED_ON))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LED_ON &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Flash
</UL>

<P><STRONG><a name="[84]"></a>LED_Toggle</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, led.o(.text.LED_Toggle))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LED_Toggle &rArr; DL_GPIO_togglePins
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_togglePins
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Flash
</UL>

<P><STRONG><a name="[87]"></a>OLED_Clear</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, oled.o(.text.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = OLED_Clear &rArr; OLED_Refresh_Gram &rArr; OLED_WR_Byte &rArr; OLED_SCLK_Set &rArr; DL_GPIO_setPins
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_Gram
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[97]"></a>OLED_DrawPoint</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, oled.o(.text.OLED_DrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[89]"></a>OLED_Init</STRONG> (Thumb, 228 bytes, Stack size 16 bytes, oled.o(.text.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_Refresh_Gram &rArr; OLED_WR_Byte &rArr; OLED_SCLK_Set &rArr; DL_GPIO_setPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_RST_Set
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_RST_Clr
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8a]"></a>OLED_RST_Clr</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, oled.o(.text.OLED_RST_Clr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_RST_Clr &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[8c]"></a>OLED_RST_Set</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, oled.o(.text.OLED_RST_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_RST_Set &rArr; DL_GPIO_setPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[90]"></a>OLED_RS_Clr</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, oled.o(.text.OLED_RS_Clr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_RS_Clr &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[91]"></a>OLED_RS_Set</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, oled.o(.text.OLED_RS_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_RS_Set &rArr; DL_GPIO_setPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[88]"></a>OLED_Refresh_Gram</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, oled.o(.text.OLED_Refresh_Gram))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OLED_Refresh_Gram &rArr; OLED_WR_Byte &rArr; OLED_SCLK_Set &rArr; DL_GPIO_setPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[92]"></a>OLED_SCLK_Clr</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, oled.o(.text.OLED_SCLK_Clr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_SCLK_Clr &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[93]"></a>OLED_SCLK_Set</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, oled.o(.text.OLED_SCLK_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_SCLK_Set &rArr; DL_GPIO_setPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[94]"></a>OLED_SDIN_Clr</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, oled.o(.text.OLED_SDIN_Clr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_SDIN_Clr &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[95]"></a>OLED_SDIN_Set</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, oled.o(.text.OLED_SDIN_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_SDIN_Set &rArr; DL_GPIO_setPins
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[96]"></a>OLED_ShowChar</STRONG> (Thumb, 268 bytes, Stack size 56 bytes, oled.o(.text.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNumber
</UL>

<P><STRONG><a name="[98]"></a>OLED_ShowNumber</STRONG> (Thumb, 220 bytes, Stack size 64 bytes, oled.o(.text.OLED_ShowNumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = OLED_ShowNumber &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_pow
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
</UL>

<P><STRONG><a name="[9c]"></a>OLED_ShowString</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, oled.o(.text.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
</UL>

<P><STRONG><a name="[8d]"></a>OLED_WR_Byte</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, oled.o(.text.OLED_WR_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OLED_WR_Byte &rArr; OLED_SCLK_Set &rArr; DL_GPIO_setPins
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SCLK_Set
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SDIN_Clr
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SDIN_Set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SCLK_Clr
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_RS_Clr
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_RS_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_Gram
</UL>

<P><STRONG><a name="[9d]"></a>SYSCFG_DL_ADC12_VOLTAGE_init</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SYSCFG_DL_ADC12_VOLTAGE_init &rArr; DL_ADC12_initSingleSample &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setClockConfig
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_enableConversions
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_enableInterrupt
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_clearInterruptStatus
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setSampleTime0
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_configConversionMem
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_initSingleSample
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a3]"></a>SYSCFG_DL_DMA_CH0_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = SYSCFG_DL_DMA_CH0_init &rArr; DL_DMA_initChannel &rArr; DL_DMA_setTrigger &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_initChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_init
</UL>

<P><STRONG><a name="[a4]"></a>SYSCFG_DL_DMA_init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SYSCFG_DL_DMA_init &rArr; SYSCFG_DL_DMA_CH0_init &rArr; DL_DMA_initChannel &rArr; DL_DMA_setTrigger &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_CH0_init
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a5]"></a>SYSCFG_DL_GPIO_init</STRONG> (Thumb, 292 bytes, Stack size 48 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_GPIO_init &rArr; DL_GPIO_enableInterrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableInterrupt
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearInterruptStatus
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setUpperPinsPolarity
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInput
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralInputFunction
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralOutputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[af]"></a>SYSCFG_DL_PWM_0_init</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = SYSCFG_DL_PWM_0_init &rArr; DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCCPDirection
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b3]"></a>SYSCFG_DL_SYSCTL_init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SYSCFG_DL_SYSCTL_init &rArr; DL_SYSCTL_configSYSPLL
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setULPCLKDivider
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableSYSPLL
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableHFXT
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setFlashWaitState
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[ba]"></a>SYSCFG_DL_SYSTICK_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SYSCFG_DL_SYSTICK_init &rArr; DL_SYSTICK_init
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSTICK_enable
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSTICK_init
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[bd]"></a>SYSCFG_DL_TIMER_0_init</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SYSCFG_DL_TIMER_0_init &rArr; DL_Timer_initTimerMode &rArr; DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableInterrupt
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[bf]"></a>SYSCFG_DL_UART_0_init</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_0_init &rArr; DL_UART_setTXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableLoopbackMode
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setTXFIFOThreshold
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setRXFIFOThreshold
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableFIFOs
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[c4]"></a>SYSCFG_DL_UART_1_init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_1_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableDMAReceiveEvent
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableInterrupt
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[c7]"></a>SYSCFG_DL_init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = SYSCFG_DL_init &rArr; SYSCFG_DL_PWM_0_init &rArr; DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_VOLTAGE_init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c8]"></a>SYSCFG_DL_initPower</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_initPower))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SYSCFG_DL_initPower &rArr; DL_Common_delayCycles
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_enablePower
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enablePower
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enablePower
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enablePower
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_reset
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_reset
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_reset
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[d2]"></a>Set_PWM</STRONG> (Thumb, 352 bytes, Stack size 80 bytes, motor.o(.text.Set_PWM))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Set_PWM &rArr; DL_Timer_setCaptureCompareValue
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[49]"></a>Systick_getTick</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, board.o(.text.Systick_getTick))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BTBufferHandler
</UL>

<P><STRONG><a name="[13]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 276 bytes, Stack size 56 bytes, control.o(.text.TIMG0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = TIMG0_IRQHandler &rArr; Get_Velocity_From_Encoder &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Right
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PI_Left
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Target_Encoder
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Velocity_From_Encoder
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_getPendingInterrupt
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IRDM_line_inspection
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_PWM
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Flash
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UART1_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, uart_callback.o(.text.UART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = UART1_IRQHandler &rArr; BT_DAMConfig &rArr; DL_DMA_setTransferSize
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BT_DAMConfig
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveData
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getPendingInterrupt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>bt_control</STRONG> (Thumb, 548 bytes, Stack size 56 bytes, uart_callback.o(.text.bt_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = bt_control &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BTBufferHandler
</UL>

<P><STRONG><a name="[8b]"></a>delay_ms</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, board.o(.text.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = delay_ms &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[d9]"></a>delay_us</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, board.o(.text.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Systick_getTick
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[20]"></a>fputc</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, board.o(.text.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = fputc &rArr; DL_UART_transmitDataBlocking &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitDataBlocking
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_isBusy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[db]"></a>keyValue</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, key.o(.text.keyValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = keyValue &rArr; DL_GPIO_readPins
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_readPins
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_scan
</UL>

<P><STRONG><a name="[81]"></a>key_scan</STRONG> (Thumb, 356 bytes, Stack size 40 bytes, key.o(.text.key_scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = key_scan &rArr; keyValue &rArr; DL_GPIO_readPins
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyValue
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key
</UL>

<P><STRONG><a name="[1e]"></a>main</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, empty.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = main &rArr; BTBufferHandler &rArr; bt_control &rArr; pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BTBufferHandler
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_battery_volt
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_ClearPendingIRQ
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[e1]"></a>myabs</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, control.o(.text.myabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = myabs
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_show
</UL>

<P><STRONG><a name="[99]"></a>oled_pow</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, oled.o(.text.oled_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = oled_pow
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNumber
</UL>

<P><STRONG><a name="[df]"></a>oled_show</STRONG> (Thumb, 1064 bytes, Stack size 96 bytes, show.o(.text.oled_show))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = oled_show &rArr; OLED_ShowNumber &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myabs
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNumber
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_Gram
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e2]"></a>__0printf</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[102]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[103]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[104]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[46]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = printf
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;APP_Show
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IRDM_line_inspection
</UL>

<P><STRONG><a name="[3d]"></a>__ARM_clz</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, depilogue.o(i.__ARM_clz))
<BR><BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[f1]"></a>__ARM_fpclassify</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[e4]"></a>__kernel_poly</STRONG> (Thumb, 172 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[e5]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[e6]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[e7]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[e8]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_dbl_overflow &rArr; __ARM_scalbn
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[ea]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_dbl_underflow &rArr; __ARM_scalbn
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[105]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[106]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[107]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[ef]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>

<P><STRONG><a name="[d8]"></a>pow</STRONG> (Thumb, 2506 bytes, Stack size 120 bytes, pow.o(i.pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bt_control
</UL>

<P><STRONG><a name="[f0]"></a>sqrt</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pow
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[c9]"></a>DL_GPIO_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[ca]"></a>DL_Timer_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[cb]"></a>DL_UART_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[cc]"></a>DL_ADC12_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_ADC12_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[cd]"></a>DL_GPIO_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[ce]"></a>DL_Timer_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[cf]"></a>DL_UART_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[d0]"></a>DL_ADC12_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_ADC12_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[a6]"></a>DL_GPIO_initPeripheralOutputFunction</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_initPeripheralOutputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[a7]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[a8]"></a>DL_GPIO_initPeripheralInputFunction</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_initPeripheralInputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[a9]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[aa]"></a>DL_GPIO_initDigitalInput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalInput
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[ab]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[ac]"></a>DL_GPIO_setUpperPinsPolarity</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setUpperPinsPolarity
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[ad]"></a>DL_GPIO_clearInterruptStatus</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[ae]"></a>DL_GPIO_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[b4]"></a>DL_SYSCTL_setBORThreshold</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[59]"></a>DL_SYSCTL_setFlashWaitState</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setFlashWaitState &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[5a]"></a>DL_SYSCTL_setSYSOSCFreq</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setSYSOSCFreq &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[b5]"></a>DL_SYSCTL_disableHFXT</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[b6]"></a>DL_SYSCTL_disableSYSPLL</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[5b]"></a>DL_SYSCTL_setULPCLKDivider</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setULPCLKDivider &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[b9]"></a>__NVIC_SetPriority</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[b1]"></a>DL_Timer_enableClock</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_enableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[b2]"></a>DL_Timer_setCCPDirection</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCCPDirection
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[be]"></a>DL_Timer_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
</UL>

<P><STRONG><a name="[6c]"></a>DL_UART_setOversampling</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_UART_setOversampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_UART_setOversampling &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[6b]"></a>DL_UART_setBaudRateDivisor</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[c1]"></a>DL_UART_enableFIFOs</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enableFIFOs))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enableFIFOs
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[6d]"></a>DL_UART_setRXFIFOThreshold</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_UART_setRXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[6e]"></a>DL_UART_setTXFIFOThreshold</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_UART_setTXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[c2]"></a>DL_UART_enableLoopbackMode</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enableLoopbackMode
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[c3]"></a>DL_UART_enable</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[c5]"></a>DL_UART_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_UART_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
</UL>

<P><STRONG><a name="[c6]"></a>DL_UART_enableDMAReceiveEvent</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_enableDMAReceiveEvent
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
</UL>

<P><STRONG><a name="[51]"></a>DL_ADC12_initSingleSample</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_ADC12_initSingleSample &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_VOLTAGE_init
</UL>

<P><STRONG><a name="[9e]"></a>DL_ADC12_configConversionMem</STRONG> (Thumb, 74 bytes, Stack size 28 bytes, ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DL_ADC12_configConversionMem
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_VOLTAGE_init
</UL>

<P><STRONG><a name="[9f]"></a>DL_ADC12_setSampleTime0</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_ADC12_setSampleTime0
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_VOLTAGE_init
</UL>

<P><STRONG><a name="[a0]"></a>DL_ADC12_clearInterruptStatus</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_ADC12_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_VOLTAGE_init
</UL>

<P><STRONG><a name="[a1]"></a>DL_ADC12_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_ADC12_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_VOLTAGE_init
</UL>

<P><STRONG><a name="[a2]"></a>DL_ADC12_enableConversions</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_ADC12_enableConversions))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_enableConversions
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_VOLTAGE_init
</UL>

<P><STRONG><a name="[bb]"></a>DL_SYSTICK_init</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SYSTICK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSTICK_init
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
</UL>

<P><STRONG><a name="[bc]"></a>DL_SYSTICK_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSTICK_enable))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
</UL>

<P><STRONG><a name="[52]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_initSingleSample
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setTXFIFOThreshold
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setRXFIFOThreshold
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setULPCLKDivider
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setFlashWaitState
</UL>

<P><STRONG><a name="[dd]"></a>__NVIC_ClearPendingIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, empty.o(.text.__NVIC_ClearPendingIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[de]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, empty.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[da]"></a>DL_UART_isBusy</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, board.o(.text.DL_UART_isBusy))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_isBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[69]"></a>DL_UART_disable</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, dl_uart.o(.text.DL_UART_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>

<P><STRONG><a name="[6a]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>

<P><STRONG><a name="[70]"></a>DL_UART_isTXFIFOFull</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, dl_uart.o(.text.DL_UART_isTXFIFOFull))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_isTXFIFOFull
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitDataBlocking
</UL>

<P><STRONG><a name="[71]"></a>DL_UART_transmitData</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_transmitData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_transmitData
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitDataBlocking
</UL>

<P><STRONG><a name="[62]"></a>DL_Timer_setLoadValue</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setLoadValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setLoadValue
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
</UL>

<P><STRONG><a name="[63]"></a>DL_Timer_setCounterValueAfterEnable</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_Timer_setCounterValueAfterEnable &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
</UL>

<P><STRONG><a name="[64]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareAction
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareInput
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareCtl
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterValueAfterEnable
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
</UL>

<P><STRONG><a name="[56]"></a>DL_DMA_configTransfer</STRONG> (Thumb, 84 bytes, Stack size 44 bytes, dl_dma.o(.text.DL_DMA_configTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = DL_DMA_configTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_initChannel
</UL>

<P><STRONG><a name="[57]"></a>DL_DMA_setTrigger</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, dl_dma.o(.text.DL_DMA_setTrigger))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_DMA_setTrigger &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_initChannel
</UL>

<P><STRONG><a name="[58]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_dma.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_setTrigger
</UL>

<P><STRONG><a name="[54]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_adc12.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setClockConfig
</UL>

<P><STRONG><a name="[8f]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, oled.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_RST_Set
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SCLK_Set
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SDIN_Set
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_RS_Set
</UL>

<P><STRONG><a name="[8e]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, oled.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_RST_Clr
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SDIN_Clr
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SCLK_Clr
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_RS_Clr
</UL>

<P><STRONG><a name="[dc]"></a>DL_GPIO_readPins</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, key.o(.text.DL_GPIO_readPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_readPins
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;keyValue
</UL>

<P><STRONG><a name="[85]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, led.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_ON
</UL>

<P><STRONG><a name="[86]"></a>DL_GPIO_togglePins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, led.o(.text.DL_GPIO_togglePins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_togglePins
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Toggle
</UL>

<P><STRONG><a name="[d3]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, motor.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_PWM
</UL>

<P><STRONG><a name="[d4]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, motor.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_PWM
</UL>

<P><STRONG><a name="[72]"></a>DL_GPIO_getEnabledInterruptStatus</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, encoder.o(.text.DL_GPIO_getEnabledInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_getEnabledInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[73]"></a>DL_GPIO_readPins</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, encoder.o(.text.DL_GPIO_readPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_readPins
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[74]"></a>DL_GPIO_clearInterruptStatus</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, encoder.o(.text.DL_GPIO_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[79]"></a>DL_ADC12_startConversion</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, adc.o(.text.DL_ADC12_startConversion))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_startConversion
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_battery_volt
</UL>

<P><STRONG><a name="[7a]"></a>DL_ADC12_getMemResult</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, adc.o(.text.DL_ADC12_getMemResult))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_ADC12_getMemResult
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_battery_volt
</UL>

<P><STRONG><a name="[41]"></a>DL_ADC12_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, adc.o(.text.DL_ADC12_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>

<P><STRONG><a name="[7c]"></a>DL_GPIO_readPins</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ir_module.o(.text.DL_GPIO_readPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_readPins
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IRDM_line_inspection
</UL>

<P><STRONG><a name="[d5]"></a>DL_Timer_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, control.o(.text.DL_Timer_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[4c]"></a>DL_DMA_disableChannel</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, uart_callback.o(.text.DL_DMA_disableChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DL_DMA_disableChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BT_DAMConfig
</UL>

<P><STRONG><a name="[4d]"></a>DL_DMA_setSrcAddr</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, uart_callback.o(.text.DL_DMA_setSrcAddr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_DMA_setSrcAddr
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BT_DAMConfig
</UL>

<P><STRONG><a name="[4e]"></a>DL_DMA_setDestAddr</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, uart_callback.o(.text.DL_DMA_setDestAddr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_DMA_setDestAddr
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BT_DAMConfig
</UL>

<P><STRONG><a name="[4f]"></a>DL_DMA_setTransferSize</STRONG> (Thumb, 44 bytes, Stack size 20 bytes, uart_callback.o(.text.DL_DMA_setTransferSize))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_DMA_setTransferSize
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BT_DAMConfig
</UL>

<P><STRONG><a name="[50]"></a>DL_DMA_enableChannel</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, uart_callback.o(.text.DL_DMA_enableChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DL_DMA_enableChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BT_DAMConfig
</UL>

<P><STRONG><a name="[48]"></a>DL_DMA_getTransferSize</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, uart_callback.o(.text.DL_DMA_getTransferSize))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DL_DMA_getTransferSize
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BTBufferHandler
</UL>

<P><STRONG><a name="[d6]"></a>DL_UART_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, uart_callback.o(.text.DL_UART_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[d7]"></a>DL_UART_receiveData</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, uart_callback.o(.text.DL_UART_receiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_receiveData
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[eb]"></a>_fp_digits</STRONG> (Thumb, 344 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e3]"></a>_printf_core</STRONG> (Thumb, 1754 bytes, Stack size 128 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[ee]"></a>_printf_post_padding</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ed]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
