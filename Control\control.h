/***********************************************
????????????????????????
????WHEELTEC
??????wheeltec.net
????????shop114407458.taobao.com 
?????: https://minibalance.aliexpress.com/store/4455017
?��??5.7
??????2021-04-29

 
Brand: WHEELTEC
Website: wheeltec.net
Taobao shop: shop114407458.taobao.com 
Aliexpress: https://minibalance.aliexpress.com/store/4455017
Version:5.7
Update??2021-04-29

All rights reserved
***********************************************/
#ifndef __CONTROL_H
#define __CONTROL_H
#include "board.h"

extern int Sensor_Left,Sensor_Middle,Sensor_Right,Sensor;
#define Frequency	200.0f			//?5ms?????��????????
#define Perimeter	0.2104867	    //???????(??��:m)=0.67*3.1415926
#define Wheelspacing 0.1610f		//?????????(??��:m)
#define PI 3.1415926
//???????????????????
typedef struct  
{
	float Current_Encoder;     	//??????????????????????
	float Motor_Pwm;     		//???PWM????????????????
	float Target_Encoder;  		//?????????????????????????????
	float Velocity; 	 		//???????
}Motor_parameter;

//??????????
typedef struct  
{
  int A;      
  int B;  
}Encoder;
extern float Move_X,Move_Z;						//????????????????
extern Encoder OriginalEncoder; 					//????????????   
extern Motor_parameter MotorA,MotorB;				//????????????
extern float Voltage_Count,Voltage_All,Voltage;
extern float Velocity_KP,Velocity_KI;	
extern int Run_Mode;//��????????
void TIM6_Init(void); 
void Get_Velocity_From_Encoder(int Encoder1,int Encoder2);
float target_limit_float(float insert,float low,float high);
int target_limit_int(int insert,int low,int high);
void Get_Target_Encoder(float Vx,float Vz);
int Incremental_PI_Left (float Encoder,float Target);
int Incremental_PI_Right (float Encoder,float Target);
void Get_Motor_PWM(void);
void Set_Pwm(int motor_a,int motor_b);
int Turn_Off(void);
int myabs(int a);
// void Get_RC(void); // ��ɾ��ң�ع���
void Key(void);
#endif
