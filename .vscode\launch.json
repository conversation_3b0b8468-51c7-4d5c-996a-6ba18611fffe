{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/比赛/25电赛/训练/ZLC_MSPM0_Peripheral_Library-main/ZLC_MSPM0_Peripheral_Library/WHEELTEC_C07A_IRF_CAR (5)/WHEELTEC_C07A_IRF_CAR", "program": "d:/比赛/25电赛/训练/ZLC_MSPM0_Peripheral_Library-main/ZLC_MSPM0_Peripheral_Library/WHEELTEC_C07A_IRF_CAR (5)/WHEELTEC_C07A_IRF_CAR/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}